#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重试失败照片 - 带图片预处理
Retry Failed Photos - With Image Preprocessing
"""

import os
import time
from pathlib import Path
from PIL import Image
from debug_single_test import test_single_photo

def resize_image_if_needed(image_path, max_size=(1024, 1024), max_file_size_kb=200):
    """
    如果图片过大则调整尺寸
    
    Args:
        image_path: 图片路径
        max_size: 最大尺寸 (width, height)
        max_file_size_kb: 最大文件大小(KB)
    
    Returns:
        str: 处理后的图片路径
    """
    original_path = Path(image_path)
    
    # 检查文件大小
    file_size_kb = os.path.getsize(image_path) / 1024
    
    with Image.open(image_path) as img:
        width, height = img.size
        
        print(f"📏 原始尺寸: {width}x{height}")
        print(f"📁 原始大小: {file_size_kb:.1f} KB")
        
        # 判断是否需要调整
        needs_resize = (
            width > max_size[0] or 
            height > max_size[1] or 
            file_size_kb > max_file_size_kb
        )
        
        if not needs_resize:
            print("✅ 图片尺寸合适，无需调整")
            return image_path
        
        print("🔄 图片过大，开始调整...")
        
        # 计算新尺寸（保持宽高比）
        ratio = min(max_size[0] / width, max_size[1] / height)
        new_width = int(width * ratio)
        new_height = int(height * ratio)
        
        print(f"📐 调整后尺寸: {new_width}x{new_height}")
        
        # 调整图片
        resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 保存调整后的图片
        resized_path = original_path.parent / f"{original_path.stem}_resized{original_path.suffix}"
        
        # 调整质量以控制文件大小
        quality = 85
        if file_size_kb > 300:
            quality = 75
        elif file_size_kb > 500:
            quality = 65
            
        resized_img.save(resized_path, format='JPEG', quality=quality, optimize=True)
        
        # 检查调整后的文件大小
        new_file_size_kb = os.path.getsize(resized_path) / 1024
        print(f"📁 调整后大小: {new_file_size_kb:.1f} KB")
        print(f"✅ 图片已保存: {resized_path}")
        
        return str(resized_path)

def retry_failed_photos_with_preprocessing():
    """重试失败的照片，先进行预处理"""
    
    # 服装模板
    clothes_image = "1111/微信图片_20250816210622_26_328.jpg"
    
    # 失败的照片列表
    failed_photos = [
        "1111/微信图片_2025-08-19_095051_534.jpg",
        "1111/微信图片_2025-08-19_095124_909.jpg", 
        "1111/微信图片_2025-08-19_095132_429.jpg"
    ]
    
    print("🔄 重试失败的照片 - 带预处理")
    print("=" * 80)
    
    results = []
    
    for i, photo in enumerate(failed_photos, 1):
        print(f"\n🔍 处理 {i}/{len(failed_photos)}: {Path(photo).name}")
        print("-" * 60)
        
        if not os.path.exists(photo):
            print(f"❌ 文件不存在: {photo}")
            results.append({"photo": photo, "success": False, "reason": "文件不存在"})
            continue
        
        try:
            # 步骤1: 预处理图片
            print("🔧 步骤1: 图片预处理")
            processed_photo = resize_image_if_needed(photo)
            
            # 步骤2: 测试换装
            print("\n🧪 步骤2: 测试换装")
            success = test_single_photo(processed_photo, clothes_image)
            
            if success:
                print(f"✅ {Path(photo).name} 重试成功！")
                results.append({"photo": photo, "success": True, "processed_path": processed_photo})
            else:
                print(f"❌ {Path(photo).name} 重试仍然失败")
                results.append({"photo": photo, "success": False, "reason": "换装失败", "processed_path": processed_photo})
                
        except Exception as e:
            print(f"❌ 处理异常: {str(e)}")
            results.append({"photo": photo, "success": False, "reason": f"异常: {str(e)}"})
        
        # 等待一下再处理下一张
        if i < len(failed_photos):
            print("\n⏳ 等待15秒后处理下一张...")
            time.sleep(15)
    
    # 显示最终结果
    print("\n" + "=" * 80)
    print("📊 重试结果汇总")
    print("=" * 80)
    
    success_count = sum(1 for r in results if r["success"])
    
    for i, result in enumerate(results, 1):
        photo_name = Path(result["photo"]).name
        if result["success"]:
            print(f"✅ {i}. {photo_name} - 成功")
        else:
            print(f"❌ {i}. {photo_name} - 失败: {result.get('reason', '未知')}")
    
    print(f"\n📈 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count > 0:
        print(f"\n🎉 有 {success_count} 张照片重试成功！")
        print("💡 建议：对于成功的照片，可以继续完成后续的背景移除和白底处理步骤")
    
    return results

def main():
    """主函数"""
    print("🚀 开始重试失败的照片（带预处理）")
    
    # 创建输出目录
    from config import OUTPUT_DIRS
    for dir_name in OUTPUT_DIRS.values():
        os.makedirs(dir_name, exist_ok=True)
    
    # 执行重试
    results = retry_failed_photos_with_preprocessing()
    
    print("\n🏁 重试完成！")

if __name__ == "__main__":
    main()
