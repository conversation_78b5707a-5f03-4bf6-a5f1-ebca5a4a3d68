# 创建换装任务（上传遮罩）

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /302/comfyui/clothes-changer/create-task-upload-mask:
    post:
      summary: 创建换装任务（上传遮罩）
      deprecated: false
      description: >-
        通过comfyUI复杂工作流实现的换装效果，商用级效果，适用于模特换装，运行时长3-5分钟


        **价格：0.1 PTC/次**


        ![5d0d47a4d463404db5bd687dd836637b.jpg](https://file.302ai.cn/gpt/imgs/20250324/5d0d47a4d463404db5bd687dd836637b.jpg)
      tags:
        - 图片处理/302.AI-ComfyUI
      parameters:
        - name: Authorization
          in: header
          description: ''
          required: true
          example: Bearer {{YOUR_API_KEY}}
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                modelImageFile:
                  format: binary
                  type: string
                  description: 进行换装操作的模特图片
                  example: >-
                    cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtOQ==/8809de6ec6c51cee2360c4d04b87de49.jpg
                clothesImageFile:
                  format: binary
                  type: string
                  description: 目标服装图片
                  example: >-
                    cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTE=/NR8AVjZiQ2dBeE5qazFNemsxTWpBMi55YW9aTi5KUGljIQcAcGhvdG9neg!!.jpg
                modelMaskFile:
                  format: binary
                  type: string
                  description: 被替换服装的遮罩图
                  example: >-
                    cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTM=/ComfyUI_00008_siuxh_1743489956.png
                clothesMaskFile:
                  format: binary
                  type: string
                  description: 目标服装的遮罩图
                  example: >-
                    cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTU=/ComfyUI_00007_dspkp_1743489934.png
              required:
                - modelImageFile
                - clothesImageFile
                - modelMaskFile
                - clothesMaskFile
            examples: {}
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  msg:
                    type: string
                  data:
                    type: object
                    properties:
                      taskId:
                        type: string
                    required:
                      - taskId
                    x-apifox-orders:
                      - taskId
                required:
                  - code
                  - msg
                  - data
                x-apifox-orders:
                  - code
                  - msg
                  - data
              example:
                code: 200
                msg: success
                data:
                  taskId: e194fed2-5148-4492-8f87-2cff0556bb21
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 图片处理/302.AI-ComfyUI
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/4012774/apis/api-281974325-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: https://api.302.ai
    description: 正式环境
  - url: https://api.302ai.cn
    description: 国内中转
security: []

```



import requests

url = "https://api.302.ai/302/comfyui/clothes-changer/create-task-upload-mask"

payload={}
files=[
   ('modelImageFile',('8809de6ec6c51cee2360c4d04b87de49.jpg',open('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtOQ==/8809de6ec6c51cee2360c4d04b87de49.jpg','rb'),'image/jpeg')),
   ('clothesImageFile',('NR8AVjZiQ2dBeE5qazFNemsxTWpBMi55YW9aTi5KUGljIQcAcGhvdG9neg!!.jpg',open('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTE=/NR8AVjZiQ2dBeE5qazFNemsxTWpBMi55YW9aTi5KUGljIQcAcGhvdG9neg!!.jpg','rb'),'image/jpeg')),
   ('modelMaskFile',('ComfyUI_00008_siuxh_1743489956.png',open('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTM=/ComfyUI_00008_siuxh_1743489956.png','rb'),'image/png')),
   ('clothesMaskFile',('ComfyUI_00007_dspkp_1743489934.png',open('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTU=/ComfyUI_00007_dspkp_1743489934.png','rb'),'image/png'))
]
headers = {
   'Authorization': 'Bearer '
}

response = requests.request("POST", url, headers=headers, data=payload, files=files)

print(response.text)


import http.client
import mimetypes
from codecs import encode

conn = http.client.HTTPSConnection("api.302.ai")
dataList = []
boundary = 'wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=modelImageFile; filename={0}'.format('8809de6ec6c51cee2360c4d04b87de49.jpg')))

fileType = mimetypes.guess_type('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtOQ==/8809de6ec6c51cee2360c4d04b87de49.jpg')[0] or 'application/octet-stream'
dataList.append(encode('Content-Type: {}'.format(fileType)))
dataList.append(encode(''))

with open('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtOQ==/8809de6ec6c51cee2360c4d04b87de49.jpg', 'rb') as f:
   dataList.append(f.read())
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=clothesImageFile; filename={0}'.format('NR8AVjZiQ2dBeE5qazFNemsxTWpBMi55YW9aTi5KUGljIQcAcGhvdG9neg!!.jpg')))

fileType = mimetypes.guess_type('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTE=/NR8AVjZiQ2dBeE5qazFNemsxTWpBMi55YW9aTi5KUGljIQcAcGhvdG9neg!!.jpg')[0] or 'application/octet-stream'
dataList.append(encode('Content-Type: {}'.format(fileType)))
dataList.append(encode(''))

with open('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTE=/NR8AVjZiQ2dBeE5qazFNemsxTWpBMi55YW9aTi5KUGljIQcAcGhvdG9neg!!.jpg', 'rb') as f:
   dataList.append(f.read())
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=modelMaskFile; filename={0}'.format('ComfyUI_00008_siuxh_1743489956.png')))

fileType = mimetypes.guess_type('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTM=/ComfyUI_00008_siuxh_1743489956.png')[0] or 'application/octet-stream'
dataList.append(encode('Content-Type: {}'.format(fileType)))
dataList.append(encode(''))

with open('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTM=/ComfyUI_00008_siuxh_1743489956.png', 'rb') as f:
   dataList.append(f.read())
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=clothesMaskFile; filename={0}'.format('ComfyUI_00007_dspkp_1743489934.png')))

fileType = mimetypes.guess_type('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTU=/ComfyUI_00007_dspkp_1743489934.png')[0] or 'application/octet-stream'
dataList.append(encode('Content-Type: {}'.format(fileType)))
dataList.append(encode(''))

with open('cmMtdXBsb2FkLTE3NDQxODg0NzI5ODEtMTU=/ComfyUI_00007_dspkp_1743489934.png', 'rb') as f:
   dataList.append(f.read())
dataList.append(encode('--'+boundary+'--'))
dataList.append(encode(''))
body = b'\r\n'.join(dataList)
payload = body
headers = {
   'Authorization': 'Bearer ',
   'Content-type': 'multipart/form-data; boundary={}'.format(boundary)
}
conn.request("POST", "/302/comfyui/clothes-changer/create-task-upload-mask", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))