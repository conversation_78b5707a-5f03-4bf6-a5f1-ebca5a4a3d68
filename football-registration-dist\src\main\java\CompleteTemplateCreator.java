import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.util.Units;

import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 创建包含所有10个球员的完整足球报名表模板
 */
public class CompleteTemplateCreator {
    
    public static void main(String[] args) {
        try {
            createCompleteTemplate();
            System.out.println("✅ 完整模板创建成功：football_template_complete.docx");
        } catch (Exception e) {
            System.err.println("❌ 模板创建失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void createCompleteTemplate() throws IOException {
        XWPFDocument document = new XWPFDocument();
        
        // 创建标题
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("{{title}}");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        
        // 创建基本信息表格
        XWPFTable infoTable = document.createTable(4, 3);
        infoTable.setWidth("100%");
        
        // 第一行：单位名称
        XWPFTableRow row1 = infoTable.getRow(0);
        row1.getCell(0).setText("单位名称(公章)");
        row1.getCell(1).setText("{{organizationName}}");
        row1.getCell(2).setText("队徽");
        
        // 第二行：领队、球员服装
        XWPFTableRow row2 = infoTable.getRow(1);
        row2.getCell(0).setText("领队");
        row2.getCell(1).setText("{{teamLeader}}");
        row2.getCell(2).setText("球员服装（颜色）");
        
        // 第三行：教练
        XWPFTableRow row3 = infoTable.getRow(2);
        row3.getCell(0).setText("教练");
        row3.getCell(1).setText("{{coach}}");
        row3.getCell(2).setText("守门员服装（颜色）");
        
        // 第四行：队医
        XWPFTableRow row4 = infoTable.getRow(3);
        row4.getCell(0).setText("队医");
        row4.getCell(1).setText("{{teamDoctor}}");
        row4.getCell(2).setText("");
        
        // 添加空行
        document.createParagraph();
        
        // 创建球员信息表格（2行5列）
        XWPFTable playerTable = document.createTable(4, 5);
        playerTable.setWidth("100%");
        
        // 第一行：球员1-5的号码和姓名
        XWPFTableRow playerRow1 = playerTable.getRow(0);
        for (int i = 0; i < 5; i++) {
            int playerNum = i + 1;
            playerRow1.getCell(i).setText("{{player" + playerNum + "Number}}号 {{player" + playerNum + "Name}}");
        }
        
        // 第二行：球员1-5的照片
        XWPFTableRow photoRow1 = playerTable.getRow(1);
        for (int i = 0; i < 5; i++) {
            int playerNum = i + 1;
            photoRow1.getCell(i).setText("{{@player" + playerNum + "Photo}}");
        }
        
        // 第三行：球员6-10的号码和姓名
        XWPFTableRow playerRow2 = playerTable.getRow(2);
        for (int i = 0; i < 5; i++) {
            int playerNum = i + 6;
            playerRow2.getCell(i).setText("{{player" + playerNum + "Number}}号 {{player" + playerNum + "Name}}");
        }
        
        // 第四行：球员6-10的照片
        XWPFTableRow photoRow2 = playerTable.getRow(3);
        for (int i = 0; i < 5; i++) {
            int playerNum = i + 6;
            photoRow2.getCell(i).setText("{{@player" + playerNum + "Photo}}");
        }
        
        // 设置表格样式
        setTableStyle(infoTable);
        setTableStyle(playerTable);
        
        // 保存文档
        try (FileOutputStream out = new FileOutputStream("football_template_complete.docx")) {
            document.write(out);
        }
        
        document.close();
    }
    
    private static void setTableStyle(XWPFTable table) {
        // 设置表格边框
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        
        // 设置单元格对齐
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                for (XWPFParagraph para : cell.getParagraphs()) {
                    para.setAlignment(ParagraphAlignment.CENTER);
                }
            }
        }
    }
}
