#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义批量处理脚本 - 处理指定的13张照片
Custom Batch Processing Script - Process specified 13 photos
"""

import os
import time
from datetime import datetime
from pathlib import Path
from batch_fashion_tryon import process_single_photo, create_output_dirs
from config import get_total_cost_per_image, get_total_cost_cny_per_image

def main():
    """主函数"""
    # 创建输出目录
    create_output_dirs()
    
    # 服装模板
    clothes_image = "1111/微信图片_20250816210622_26_328.jpg"
    
    # 指定要处理的13张照片
    target_photos = [
        "1111/微信图片_2025-08-17_134343_347.jpg",
        "1111/微信图片_2025-08-17_134416_824.jpg", 
        "1111/微信图片_2025-08-17_134425_503.jpg",
        "1111/微信图片_2025-08-17_134431_508.jpg",
        "1111/微信图片_2025-08-17_134448_769.jpg",
        "1111/微信图片_2025-08-18_030336_421.jpg",
        "1111/微信图片_2025-08-19_095017_555.jpg",
        "1111/微信图片_2025-08-19_095034_724.jpg",
        "1111/微信图片_2025-08-19_095044_853.jpg",
        "1111/微信图片_2025-08-19_095051_534.jpg",
        "1111/微信图片_2025-08-19_095058_867.jpg",
        "1111/微信图片_2025-08-19_095124_909.jpg",
        "1111/微信图片_2025-08-19_095132_429.jpg"
    ]
    
    # 验证文件存在
    valid_photos = []
    for photo in target_photos:
        if os.path.exists(photo):
            valid_photos.append(photo)
        else:
            print(f"⚠️  文件不存在，跳过: {photo}")
    
    if not valid_photos:
        print("❌ 没有找到有效的照片文件")
        return
    
    print("=" * 100)
    print("🎯 自定义批量时尚换装工具")
    print("=" * 100)
    print("📝 工作流程:")
    print("   1. 302.AI-ComfyUI 换装")
    print("   2. Clipdrop Remove-background 移除背景")
    print("   3. 本地PIL 添加白底背景")
    print()
    print(f"📊 指定处理照片: {len(valid_photos)} 张")
    print(f"👕 服装模板: {clothes_image}")
    print(f"💰 预估总成本: {len(valid_photos) * get_total_cost_per_image()} PTC (约{len(valid_photos) * get_total_cost_cny_per_image():.1f}元)")
    print()
    
    # 显示要处理的文件列表
    print("📋 处理列表:")
    for i, photo in enumerate(valid_photos, 1):
        photo_name = Path(photo).name
        print(f"   {i}. {photo_name}")
    print()
    
    # 确认处理
    confirm = input("🤔 确认开始处理吗？(y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消处理")
        return
    
    batch_start_time = time.time()
    batch_results = {
        "start_time": datetime.now().isoformat(),
        "total_photos": len(valid_photos),
        "results": [],
        "summary": {}
    }
    
    # 处理每张照片
    success_count = 0
    for i, photo_path in enumerate(valid_photos, 1):
        photo_name = Path(photo_path).stem
        print(f"\n🔄 进度: {i}/{len(valid_photos)} - {photo_name}")
        
        try:
            result = process_single_photo(photo_path, clothes_image, photo_name)
            batch_results["results"].append(result)
            
            if result.get("success", False):
                success_count += 1
                print(f"✅ {photo_name} 处理成功")
            else:
                print(f"❌ {photo_name} 处理失败")
                
        except Exception as e:
            print(f"❌ {photo_name} 处理异常: {str(e)}")
            batch_results["results"].append({
                "photo_name": photo_name,
                "success": False,
                "error": str(e)
            })
    
    # 计算总结
    batch_end_time = time.time()
    total_time = batch_end_time - batch_start_time
    
    batch_results["summary"] = {
        "total_time": total_time,
        "success_count": success_count,
        "failure_count": len(valid_photos) - success_count,
        "success_rate": (success_count / len(valid_photos)) * 100,
        "total_cost_ptc": success_count * get_total_cost_per_image(),
        "total_cost_cny": success_count * get_total_cost_cny_per_image()
    }
    
    # 显示最终结果
    print("\n" + "=" * 100)
    print("🎉 批量处理完成！")
    print("=" * 100)
    print(f"📊 处理统计:")
    print(f"   ✅ 成功: {success_count} 张")
    print(f"   ❌ 失败: {len(valid_photos) - success_count} 张")
    print(f"   📈 成功率: {batch_results['summary']['success_rate']:.1f}%")
    print(f"   ⏱️  总耗时: {total_time:.1f} 秒 (约{total_time/60:.1f}分钟)")
    print(f"   💰 实际成本: {batch_results['summary']['total_cost_ptc']} PTC (约{batch_results['summary']['total_cost_cny']:.1f}元)")
    print(f"   📁 结果目录: batch_results/")
    print("=" * 100)

if __name__ == "__main__":
    main()
