import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import com.theokanning.openai.service.OpenAiService;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * OpenAI API客户端
 */
public class OpenAIClient {
    private OpenAiService service;
    private String model;
    private int maxTokens;
    private double temperature;
    private List<ChatMessage> conversationHistory;
    
    public OpenAIClient(Properties config) {
        String apiKey = config.getProperty("openai.api.key");
        this.model = config.getProperty("openai.model", "gpt-4o-mini");
        this.maxTokens = Integer.parseInt(config.getProperty("openai.max.tokens", "2000"));
        this.temperature = Double.parseDouble(config.getProperty("openai.temperature", "0.7"));
        
        // 创建OpenAI服务，设置超时时间
        this.service = new OpenAiService(apiKey, Duration.ofSeconds(60));
        this.conversationHistory = new ArrayList<>();
        
        // 初始化系统提示
        initializeSystemPrompt();
    }
    
    private void initializeSystemPrompt() {
        String systemPrompt = "你是一个专业的足球报名表生成助手。你的任务是帮助用户收集足球队的信息，包括：\n" +
                "1. 队伍基本信息：比赛标题、单位名称、领队、教练、队医\n" +
                "2. 球员信息：最多10名球员，每个球员包括球衣号码、姓名、照片文件名\n\n" +
                "请用友好、专业的语气与用户对话，逐步收集所需信息。\n" +
                "当收集完所有信息后，请以JSON格式输出数据，格式如下：\n" +
                "{\n" +
                "  \"teamInfo\": {\n" +
                "    \"title\": \"比赛标题\",\n" +
                "    \"organizationName\": \"单位名称\",\n" +
                "    \"teamLeader\": \"领队姓名\",\n" +
                "    \"coach\": \"教练姓名\",\n" +
                "    \"teamDoctor\": \"队医姓名\"\n" +
                "  },\n" +
                "  \"players\": [\n" +
                "    {\"number\": \"10\", \"name\": \"张三\", \"photoPath\": \"photos/player1.jpg\"},\n" +
                "    {\"number\": \"9\", \"name\": \"李四\", \"photoPath\": \"photos/player2.jpg\"}\n" +
                "  ]\n" +
                "}\n\n" +
                "注意：照片路径必须包含 'photos/' 前缀，例如 'photos/player1.jpg'。";
        
        conversationHistory.add(new ChatMessage(ChatMessageRole.SYSTEM.value(), systemPrompt));
    }
    
    /**
     * 发送消息给AI并获取回复
     */
    public String sendMessage(String userMessage) {
        try {
            // 添加用户消息到对话历史
            conversationHistory.add(new ChatMessage(ChatMessageRole.USER.value(), userMessage));
            
            // 创建聊天完成请求
            ChatCompletionRequest request = ChatCompletionRequest.builder()
                    .model(model)
                    .messages(conversationHistory)
                    .maxTokens(maxTokens)
                    .temperature(temperature)
                    .build();
            
            // 发送请求并获取响应
            ChatCompletionResult result = service.createChatCompletion(request);
            String aiResponse = result.getChoices().get(0).getMessage().getContent();
            
            // 添加AI回复到对话历史
            conversationHistory.add(new ChatMessage(ChatMessageRole.ASSISTANT.value(), aiResponse));
            
            return aiResponse;
            
        } catch (Exception e) {
            System.err.println("❌ OpenAI API调用失败: " + e.getMessage());
            return "抱歉，AI服务暂时不可用，请稍后重试。";
        }
    }
    
    /**
     * 重置对话历史
     */
    public void resetConversation() {
        conversationHistory.clear();
        initializeSystemPrompt();
    }
    
    /**
     * 获取对话历史长度
     */
    public int getConversationLength() {
        return conversationHistory.size();
    }
    
    /**
     * 检查API连接
     */
    public boolean testConnection() {
        try {
            String testResponse = sendMessage("你好，请简单介绍一下你的功能。");
            return testResponse != null && !testResponse.contains("AI服务暂时不可用");
        } catch (Exception e) {
            return false;
        }
    }
}
