@echo off
echo 🚀 正在准备足球报名表生成器分发包...

:: 创建分发目录
set DIST_DIR=football-registration-dist
if exist %DIST_DIR% rmdir /s /q %DIST_DIR%
mkdir %DIST_DIR%

echo 📁 创建目录结构...
mkdir %DIST_DIR%\src\main\java
mkdir %DIST_DIR%\photos

echo 📋 复制核心文件...

:: 复制项目配置
copy pom.xml %DIST_DIR%\
copy template.docx %DIST_DIR%\

:: 复制源代码
copy src\main\java\TemplateTest.java %DIST_DIR%\src\main\java\
copy src\main\java\CompleteTemplateCreator.java %DIST_DIR%\src\main\java\
copy src\main\java\SimpleTest.java %DIST_DIR%\src\main\java\

:: 复制文档
copy README.md %DIST_DIR%\
copy 使用说明.md %DIST_DIR%\
copy 模板创建指南.md %DIST_DIR%\

:: 复制示例照片（只复制原始照片，不包含裁剪后的）
copy photos\player1.png %DIST_DIR%\photos\
copy photos\player2.jpg %DIST_DIR%\photos\
copy photos\player3.jpg %DIST_DIR%\photos\
copy photos\player4.jpg %DIST_DIR%\photos\
copy photos\player5.jpg %DIST_DIR%\photos\
copy photos\player6.jpg %DIST_DIR%\photos\
copy photos\player7.jpg %DIST_DIR%\photos\

echo 📝 创建快速启动脚本...

:: 创建Windows启动脚本
echo @echo off > %DIST_DIR%\run.bat
echo echo 🏆 足球报名表生成器 >> %DIST_DIR%\run.bat
echo echo 正在编译和运行程序... >> %DIST_DIR%\run.bat
echo mvn clean compile exec:java -Dexec.mainClass="TemplateTest" >> %DIST_DIR%\run.bat
echo echo 完成！请查看生成的 football_registration_output_*.docx 文件 >> %DIST_DIR%\run.bat
echo pause >> %DIST_DIR%\run.bat

:: 创建Linux/Mac启动脚本
echo #!/bin/bash > %DIST_DIR%\run.sh
echo echo "🏆 足球报名表生成器" >> %DIST_DIR%\run.sh
echo echo "正在编译和运行程序..." >> %DIST_DIR%\run.sh
echo mvn clean compile exec:java -Dexec.mainClass="TemplateTest" >> %DIST_DIR%\run.sh
echo echo "完成！请查看生成的 football_registration_output_*.docx 文件" >> %DIST_DIR%\run.sh

echo 📦 创建压缩包...
powershell Compress-Archive -Path %DIST_DIR%\* -DestinationPath football-registration-v1.0.zip -Force

echo ✅ 分发包准备完成！
echo 📁 分发目录：%DIST_DIR%
echo 📦 压缩包：football-registration-v1.0.zip
echo.
echo 🎯 分发包包含：
echo    ✅ 完整源代码
echo    ✅ Maven配置文件
echo    ✅ Word模板文件
echo    ✅ 示例照片
echo    ✅ 详细文档
echo    ✅ 快速启动脚本
echo.
echo 📋 接收者只需要：
echo    1. 解压文件
echo    2. 安装Java和Maven
echo    3. 运行 run.bat (Windows) 或 run.sh (Linux/Mac)
echo.
pause
