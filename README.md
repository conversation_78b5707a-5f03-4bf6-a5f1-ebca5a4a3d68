# 🏆 足球报名表生成器

基于poi-tl模板引擎的足球比赛报名表自动生成工具，支持10名球员信息和照片的智能处理。

## ✨ 功能特点

- 🎯 **自动生成**：一键生成专业的足球报名表
- 📸 **智能图片处理**：自动裁剪照片为正方形，避免变形
- 🔄 **多格式支持**：支持PNG、JPG等多种图片格式
- 📋 **完整信息**：包含队伍信息和10名球员详细资料
- 🎨 **专业模板**：使用Word模板，样式美观专业

## 🚀 快速开始

### 环境要求

- Java 8 或更高版本
- Maven 3.6 或更高版本

### 安装步骤

1. **下载项目文件**
   ```bash
   # 解压项目文件到本地目录
   ```

2. **安装依赖**
   ```bash
   mvn clean compile
   ```

3. **准备照片**
   - 将球员照片放入 `photos/` 目录
   - 支持PNG、JPG格式
   - 照片会自动裁剪为正方形

4. **运行程序**
   ```bash
   mvn exec:java -Dexec.mainClass="TemplateTest"
   ```

5. **查看结果**
   - 生成的报名表文件：`football_registration_output_*.docx`

## 📁 项目结构

```
football-registration/
├── pom.xml                     # Maven配置文件
├── template.docx               # Word模板文件
├── src/main/java/
│   ├── TemplateTest.java       # 主程序
│   ├── CompleteTemplateCreator.java  # 模板创建工具
│   └── SimpleTest.java         # 简单测试
├── photos/                     # 球员照片目录
│   ├── player1.png            # 示例照片
│   ├── player2.jpg            # 示例照片
│   └── ...
├── README.md                   # 项目说明
├── 使用说明.md                 # 详细使用指南
└── 模板创建指南.md             # 模板制作说明
```

## 🎯 使用方法

### 1. 修改球员信息

编辑 `TemplateTest.java` 中的球员数据：

```java
// 修改基本信息
data.put("title", "您的比赛名称");
data.put("organizationName", "您的单位名称");
data.put("teamLeader", "领队姓名");
data.put("coach", "教练姓名");
data.put("teamDoctor", "队医姓名");

// 修改球员信息
addPlayerData(data, 1, "球衣号码", "球员姓名", "photos/球员照片.jpg");
```

### 2. 替换照片

- 将真实球员照片放入 `photos/` 目录
- 文件名格式：`player1.jpg`, `player2.png` 等
- 程序会自动处理图片尺寸和格式

### 3. 自定义模板

如需修改模板样式：
1. 用Word打开 `template.docx`
2. 修改样式和布局
3. 保持标签格式：`{{标签名}}`、`{{@图片标签}}`

## 🔧 技术说明

### 核心技术
- **poi-tl**：Word模板引擎
- **Apache POI**：Office文档处理
- **Java ImageIO**：图片处理

### 图片处理算法
- **居中裁剪**：保留图片主要内容
- **格式保持**：JPG保存为JPG，PNG保存为PNG
- **智能缩放**：避免图片拉伸变形

## 📝 标签说明

### 基本信息标签
- `{{title}}` - 比赛标题
- `{{organizationName}}` - 单位名称
- `{{teamLeader}}` - 领队
- `{{coach}}` - 教练
- `{{teamDoctor}}` - 队医

### 球员信息标签
- `{{player1Number}}` - 球员1号码
- `{{player1Name}}` - 球员1姓名
- `{{@player1Photo}}` - 球员1照片
- ... (player1 到 player10)

## ❓ 常见问题

**Q: 支持哪些图片格式？**
A: 支持PNG、JPG、JPEG等常见格式。

**Q: 照片会变形吗？**
A: 不会。程序会智能裁剪为正方形，保持比例。

**Q: 如何修改模板样式？**
A: 用Word打开template.docx，修改样式后保存即可。

**Q: 可以生成多少个球员？**
A: 当前支持10个球员，可以修改代码扩展。

## 📄 许可证

本项目基于Apache License 2.0开源协议。

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

**作者**：[您的名字]  
**版本**：1.0.0  
**更新时间**：2025年8月
