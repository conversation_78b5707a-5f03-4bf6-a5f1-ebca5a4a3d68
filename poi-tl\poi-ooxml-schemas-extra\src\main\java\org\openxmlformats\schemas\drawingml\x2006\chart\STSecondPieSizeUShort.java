/*
 * XML Type:  ST_SecondPieSizeUShort
 * Namespace: http://schemas.openxmlformats.org/drawingml/2006/chart
 * Java type: org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizeUShort
 *
 * Automatically generated - do not modify.
 */
package org.openxmlformats.schemas.drawingml.x2006.chart;

import org.apache.xmlbeans.impl.schema.ElementFactory;
import org.apache.xmlbeans.impl.schema.AbstractDocumentFactory;
import org.apache.xmlbeans.impl.schema.DocumentFactory;
import org.apache.xmlbeans.impl.schema.SimpleTypeFactory;


/**
 * An XML ST_SecondPieSizeUShort(@http://schemas.openxmlformats.org/drawingml/2006/chart).
 *
 * This is an atomic type that is a restriction of org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizeUShort.
 */
public interface STSecondPieSizeUShort extends org.apache.xmlbeans.XmlUnsignedShort {
    SimpleTypeFactory<org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizeUShort> Factory = new SimpleTypeFactory<>(org.apache.poi.schemas.ooxml.system.ooxml.TypeSystemHolder.typeSystem, "stsecondpiesizeushort6af6type");
    org.apache.xmlbeans.SchemaType type = Factory.getType();

}
