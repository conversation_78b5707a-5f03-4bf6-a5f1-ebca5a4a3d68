# 创建换装任务

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /302/comfyui/clothes-changer/create-task:
    post:
      summary: 创建换装任务
      deprecated: false
      description: >-
        通过comfyUI复杂工作流实现的换装效果，商用级效果，适用于模特换装，运行时长3-5分钟


        **价格：0.1 PTC/次**


        ![5d0d47a4d463404db5bd687dd836637b.jpg](https://file.302ai.cn/gpt/imgs/20250324/5d0d47a4d463404db5bd687dd836637b.jpg)
      tags:
        - 图片处理/302.AI-ComfyUI
      parameters:
        - name: Authorization
          in: header
          description: ''
          required: true
          example: Bearer {{YOUR_API_KEY}}
          schema:
            type: string
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                modelImageFile:
                  format: binary
                  type: string
                  description: 进行换装操作的模特图片
                  example: cmMtdXBsb2FkLTE3NDI4MTAxNzUxOTEtMw==/4.jpg
                clothesImageFile:
                  format: binary
                  type: string
                  description: 目标服装图片
                  example: cmMtdXBsb2FkLTE3NDI4MTAxNzUxOTEtNw==/test.jpg
                modelImgSegLabels:
                  type: string
                  description: >-
                    用字符串标记模特图中期望被替换的服装类型（不同类型之间用逗号隔开）。

                    支持的类型：

                    “0”-脸部，”1“-头发，”2“-帽子，”3“-太阳眼镜，”4“-左臂部位，”5“-右臂部位，”6“-左腿部位，”7“-右腿部位，”8“-左脚的鞋子，”9“-右脚的鞋子，”10“-上衣，”11“-裙子，”12“-裤子，”13“-连衣裙，”14“-腰带，”15“-包，”16“-围巾
                  example: '13'
                clothesImgSegLabels:
                  description: >-
                    用字符串标记目标服装图中用来替换模特图的服装类型（不同类型之间用逗号隔开）。

                    支持的类型：

                    “0”-脸部，”1“-头发，”2“-帽子，”3“-太阳眼镜，”4“-左臂部位，”5“-右臂部位，”6“-左腿部位，”7“-右腿部位，”8“-左脚的鞋子，”9“-右脚的鞋子，”10“-上衣，”11“-裙子，”12“-裤子，”13“-连衣裙，”14“-腰带，”15“-包，”16“-围巾
                  example: 10,11
                  type: string
              required:
                - modelImageFile
                - clothesImageFile
                - modelImgSegLabels
                - clothesImgSegLabels
            examples: {}
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  msg:
                    type: string
                  data:
                    type: object
                    properties:
                      taskId:
                        type: string
                    required:
                      - taskId
                    x-apifox-orders:
                      - taskId
                required:
                  - code
                  - msg
                  - data
                x-apifox-orders:
                  - code
                  - msg
                  - data
              example:
                code: 200
                msg: success
                data:
                  taskId: e194fed2-5148-4492-8f87-2cff0556bb21
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 图片处理/302.AI-ComfyUI
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/4012774/apis/api-270156075-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: https://api.302.ai
    description: 正式环境
  - url: https://api.302ai.cn
    description: 国内中转
security: []

```




import http.client
import mimetypes
from codecs import encode

conn = http.client.HTTPSConnection("api.302.ai")
dataList = []
boundary = 'wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=modelImageFile; filename={0}'.format('4.jpg')))

fileType = mimetypes.guess_type('cmMtdXBsb2FkLTE3NDI4MTAxNzUxOTEtMw==/4.jpg')[0] or 'application/octet-stream'
dataList.append(encode('Content-Type: {}'.format(fileType)))
dataList.append(encode(''))

with open('cmMtdXBsb2FkLTE3NDI4MTAxNzUxOTEtMw==/4.jpg', 'rb') as f:
   dataList.append(f.read())
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=clothesImageFile; filename={0}'.format('test.jpg')))

fileType = mimetypes.guess_type('cmMtdXBsb2FkLTE3NDI4MTAxNzUxOTEtNw==/test.jpg')[0] or 'application/octet-stream'
dataList.append(encode('Content-Type: {}'.format(fileType)))
dataList.append(encode(''))

with open('cmMtdXBsb2FkLTE3NDI4MTAxNzUxOTEtNw==/test.jpg', 'rb') as f:
   dataList.append(f.read())
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=modelImgSegLabels;'))

dataList.append(encode('Content-Type: {}'.format('text/plain')))
dataList.append(encode(''))

dataList.append(encode("13"))
dataList.append(encode('--' + boundary))
dataList.append(encode('Content-Disposition: form-data; name=clothesImgSegLabels;'))

dataList.append(encode('Content-Type: {}'.format('text/plain')))
dataList.append(encode(''))

dataList.append(encode("10,11"))
dataList.append(encode('--'+boundary+'--'))
dataList.append(encode(''))
body = b'\r\n'.join(dataList)
payload = body
headers = {
   'Authorization': 'Bearer ',
   'Content-type': 'multipart/form-data; boundary={}'.format(boundary)
}
conn.request("POST", "/302/comfyui/clothes-changer/create-task", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))


