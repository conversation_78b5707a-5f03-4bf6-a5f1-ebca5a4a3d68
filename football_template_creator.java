import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.util.TableTools;
import java.util.*;

/**
 * 足球报名表模板创建器
 * 这个类演示如何创建Word模板文件
 */
public class FootballTemplateCreator {
    
    public static void main(String[] args) throws Exception {
        createTemplate();
        System.out.println("模板文件已创建：football_registration_template.docx");
    }
    
    /**
     * 创建足球报名表模板
     */
    public static void createTemplate() throws Exception {
        // 创建文档内容
        DocumentRenderData template = Documents.of()
            // 标题
            .addParagraph(Paragraphs.of("{{title}}")
                .center()
                .create())
            
            // 空行
            .addParagraph(Paragraphs.of("").create())
            
            // 基本信息表格
            .addTable(createBasicInfoTable())
            
            // 空行
            .addParagraph(Paragraphs.of("").create())
            
            // 球员信息表格占位符
            .addParagraph(Paragraphs.of("{{#playerTable}}").create())
            
            // 空行
            .addParagraph(Paragraphs.of("").create())
            
            // 附件表格占位符
            .addParagraph(Paragraphs.of("附件1 {{title}}").create())
            .addTable(createAttachmentTable())
            
            .create();
            
        // 生成模板文件
        XWPFTemplate.create(template)
            .writeToFile("football_registration_template.docx");
    }
    
    /**
     * 创建基本信息表格
     */
    private static TableRenderData createBasicInfoTable() {
        // 表头行
        RowRenderData headerRow = Rows.of("单位名称(公章)", "{{organizationName}}", "队徽")
            .bgColor("E8F5E8")
            .center()
            .create();
            
        // 领队行
        RowRenderData leaderRow = Rows.of("领队", "姓名", "{{teamLeader}}")
            .center()
            .create();
            
        // 教练行  
        RowRenderData coachRow = Rows.of("教练", "姓名", "{{coach}}")
            .center()
            .create();
            
        // 球衣颜色行
        RowRenderData jerseyRow = Rows.of("球衣", "主场球衣（新色）", "{{jerseyColor}}")
            .center()
            .create();
            
        TableRenderData table = Tables.ofA4Width()
            .addRow(headerRow)
            .addRow(leaderRow) 
            .addRow(coachRow)
            .addRow(jerseyRow)
            .create();
            
        // 设置单元格合并
        MergeCellRule mergeRule = MergeCellRule.builder()
            .map(Grid.of(0, 1), Grid.of(0, 2)) // 合并第一行的后两列
            .build();
        table.setMergeRule(mergeRule);
        
        return table;
    }
    
    /**
     * 创建附件表格模板
     */
    private static TableRenderData createAttachmentTable() {
        // 表头
        RowRenderData header1 = Rows.of("单位名称(公章)", "", "队徽")
            .bgColor("E8F5E8")
            .center()
            .create();
            
        RowRenderData header2 = Rows.of("领队", "球员服装（新色）", "守门员服装（新色）")
            .center()
            .create();
            
        RowRenderData header3 = Rows.of("教练", "球衣", "球裤", "球衣", "球裤", "球袜")
            .center()
            .create();
            
        // 数据行（空白，供手动填写）
        RowRenderData dataRow1 = Rows.of("教练", "A", "", "", "", "")
            .center()
            .create();
            
        RowRenderData dataRow2 = Rows.of("队医", "B", "", "", "", "")
            .center()
            .create();
            
        // 创建表格
        TableRenderData table = Tables.ofA4Width()
            .addRow(header1)
            .addRow(header2)
            .addRow(header3)
            .addRow(dataRow1)
            .addRow(dataRow2)
            .create();
            
        // 设置复杂的单元格合并
        MergeCellRule mergeRule = MergeCellRule.builder()
            .map(Grid.of(0, 1), Grid.of(0, 2)) // 第一行合并
            .map(Grid.of(1, 1), Grid.of(1, 3)) // 第二行合并
            .map(Grid.of(1, 4), Grid.of(1, 6)) // 第二行合并
            .build();
        table.setMergeRule(mergeRule);
        
        return table;
    }
}
