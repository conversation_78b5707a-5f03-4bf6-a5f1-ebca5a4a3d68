# 🤖 AI足球报名表生成器

基于OpenAI GPT-4o-mini的智能足球报名表生成工具。通过自然语言对话收集球员信息，自动生成专业的Word报名表。

## ✨ 功能特点

- 🤖 **AI对话交互**：使用GPT-4o-mini进行自然语言对话
- 📝 **智能数据解析**：自动从对话中提取结构化球员信息
- 🎯 **一键生成**：自动生成专业的足球报名表
- 📸 **图片处理**：自动裁剪球员照片为正方形
- 🔄 **批量处理**：支持连续生成多份报名表

## 🚀 快速开始

### 环境要求

- Java 8 或更高版本
- Maven 3.6 或更高版本
- OpenAI API 密钥

### 安装步骤

1. **配置API密钥**
   - 编辑 `config.properties` 文件
   - 填入您的OpenAI API密钥

2. **安装依赖**
   ```bash
   mvn clean compile
   ```

3. **运行程序**
   ```bash
   # Windows
   run-ai.bat
   
   # 或手动运行
   mvn exec:java -Dexec.mainClass="AIFootballGenerator"
   ```

## 💬 使用示例

### 对话示例

```
🤖 AI助手：你好！我是足球报名表生成助手。请告诉我您的队伍信息，包括比赛名称、单位名称、领队、教练、队医，以及球员的号码、姓名和照片信息。

👤 您：我们要参加2025年五人制足球比赛，单位是太河镇人民政府，领队是李四，教练是张三，队医是王五。球员有：10号张雷、0号白浩、3号翟召昌。

🤖 AI助手：好的，我已经记录了基本信息。请问还有其他球员吗？另外，请提供球员照片的文件路径...

👤 您：照片都在photos目录下，张雷的照片是player1.png，白浩的是player2.jpg，翟召昌的是player3.jpg

🤖 AI助手：信息收集完成！以下是整理好的数据：
{
  "teamInfo": {
    "title": "2025年五人制足球比赛报名表",
    "organizationName": "太河镇人民政府",
    "teamLeader": "李四",
    "coach": "张三",
    "teamDoctor": "王五"
  },
  "players": [
    {"number": "10", "name": "张雷", "photoPath": "photos/player1.png"},
    {"number": "0", "name": "白浩", "photoPath": "photos/player2.jpg"},
    {"number": "3", "name": "翟召昌", "photoPath": "photos/player3.jpg"}
  ]
}
```

## 📁 项目结构

```
ai-football-generator/
├── src/main/java/
│   ├── AIFootballGenerator.java    # 主程序
│   ├── OpenAIClient.java          # OpenAI API客户端
│   ├── PlayerDataParser.java      # 数据解析器
│   ├── FootballReportGenerator.java # 报名表生成器
│   └── PlayerData.java            # 数据模型
├── photos/                         # 球员照片目录
├── output/                         # 生成的报名表输出目录
├── template.docx                   # Word模板文件
├── config.properties               # 配置文件
├── pom.xml                        # Maven配置
├── run-ai.bat                     # 启动脚本
└── README.md                      # 说明文档
```

## ⚙️ 配置说明

### config.properties

```properties
# OpenAI API配置
openai.api.key=your-api-key-here
openai.model=gpt-4o-mini
openai.max.tokens=2000
openai.temperature=0.7

# 文件路径配置
template.path=template.docx
output.directory=output/
photos.directory=photos/
```

## 🎯 使用技巧

### 1. 球员信息输入

可以用多种方式输入球员信息：

```
# 方式1：逐个输入
"第一个球员是10号张雷，照片是player1.png"

# 方式2：批量输入
"球员有：10号张雷、9号李四、8号王五，照片分别是player1.png、player2.jpg、player3.jpg"

# 方式3：表格形式
"球员信息如下：
10号 张雷 photos/player1.png
9号 李四 photos/player2.jpg
8号 王五 photos/player3.jpg"
```

### 2. 照片文件

- 支持PNG、JPG、JPEG格式
- 照片会自动裁剪为正方形
- 建议照片尺寸不小于200x200像素

### 3. 数据修改

如果信息有误，可以直接告诉AI：
```
"刚才的教练名字错了，应该是赵六"
"请把10号球员的照片改为player10.jpg"
```

## 🔧 技术架构

- **OpenAI GPT-4o-mini**：自然语言处理和数据提取
- **poi-tl**：Word模板引擎
- **Jackson**：JSON数据处理
- **Apache POI**：Office文档操作
- **Java ImageIO**：图片处理

## ❓ 常见问题

**Q: API调用失败怎么办？**
A: 检查网络连接和API密钥是否正确，确保有足够的API额度。

**Q: 照片无法显示？**
A: 确保照片文件存在且路径正确，支持的格式为PNG、JPG、JPEG。

**Q: 生成的报名表格式不对？**
A: 检查template.docx模板文件是否包含正确的标签格式。

**Q: 如何修改模板样式？**
A: 用Word打开template.docx，修改样式后保存，保持标签格式不变。

## 📄 许可证

本项目基于Apache License 2.0开源协议。

---

**版本**：1.0.0  
**更新时间**：2025年8月
