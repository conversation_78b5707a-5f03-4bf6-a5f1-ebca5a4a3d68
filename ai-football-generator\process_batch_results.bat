@echo off
echo Processing batch_results photos to Word template...
echo ================================================

REM Check Java environment
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java not found, please install Java 8 or higher
    pause
    exit /b 1
)

REM Check Maven environment
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Maven not found, please install Maven
    pause
    exit /b 1
)

REM Check required files
if not exist "batch_results\" (
    echo ERROR: batch_results directory not found
    pause
    exit /b 1
)

if not exist "template_15players.docx" (
    echo ERROR: template_15players.docx not found
    pause
    exit /b 1
)

echo Environment check passed

REM Compile project
echo Compiling project...
mvn compile -q
if %errorlevel% neq 0 (
    echo Compilation failed
    pause
    exit /b 1
)

echo Compilation successful

REM Download dependencies
echo Downloading dependencies...
mvn dependency:copy-dependencies -q
if %errorlevel% neq 0 (
    echo Dependency download failed
    pause
    exit /b 1
)

REM Run batch processor
echo Processing batch_results photos...
java -cp "target/classes;target/dependency/*" BatchResultsProcessor

if %errorlevel% equ 0 (
    echo.
    echo Processing completed!
    echo Please check output directory for generated files
    echo.

    REM Open output directory
    if exist "output\" (
        echo Opening output directory...
        start "" "output"
    )
) else (
    echo Processing failed, please check error messages
)

echo.
pause
