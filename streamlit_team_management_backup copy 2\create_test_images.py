#!/usr/bin/env python3
"""
创建测试图片文件用于演示批量上传功能
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_player_image(name, number, size=(300, 400), bg_color=(100, 150, 200)):
    """创建测试球员图片"""
    # 创建图片
    img = Image.new('RGB', size, bg_color)
    draw = ImageDraw.Draw(img)
    
    # 尝试使用系统字体，如果没有就使用默认字体
    try:
        font_large = ImageFont.truetype("arial.ttf", 40)
        font_small = ImageFont.truetype("arial.ttf", 30)
    except:
        font_large = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # 绘制球员信息
    # 绘制号码
    number_text = f"#{number}"
    bbox = draw.textbbox((0, 0), number_text, font=font_large)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (size[0] - text_width) // 2
    y = size[1] // 3
    draw.text((x, y), number_text, fill=(255, 255, 255), font=font_large)
    
    # 绘制姓名
    bbox = draw.textbbox((0, 0), name, font=font_small)
    text_width = bbox[2] - bbox[0]
    x = (size[0] - text_width) // 2
    y = size[1] // 2
    draw.text((x, y), name, fill=(255, 255, 255), font=font_small)
    
    # 绘制简单的人形轮廓
    center_x = size[0] // 2
    head_y = size[1] // 4
    
    # 头部
    draw.ellipse([center_x-20, head_y-20, center_x+20, head_y+20], fill=(255, 200, 150))
    
    # 身体
    draw.rectangle([center_x-15, head_y+20, center_x+15, head_y+80], fill=(50, 100, 150))
    
    return img

def main():
    """创建测试图片"""
    # 创建测试图片目录
    test_dir = "test_images"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 测试球员数据
    test_players = [
        ("张三", 6),
        ("李四", 7), 
        ("王五", 8),
        ("赵六", 9),
        ("钱七", 10)
    ]
    
    print("创建测试球员图片...")
    
    for name, number in test_players:
        # 为每个球员创建不同颜色的背景
        bg_colors = [
            (100, 150, 200),  # 蓝色
            (150, 100, 200),  # 紫色
            (200, 150, 100),  # 橙色
            (100, 200, 150),  # 绿色
            (200, 100, 150),  # 粉色
        ]
        
        bg_color = bg_colors[number % len(bg_colors)]
        img = create_test_player_image(name, number, bg_color=bg_color)
        
        filename = f"{test_dir}/player_{number}_{name}.jpg"
        img.save(filename, "JPEG", quality=95)
        print(f"创建: {filename}")
    
    print(f"\n✅ 成功创建 {len(test_players)} 张测试图片")
    print(f"📁 图片保存在: {test_dir}/")
    print("\n💡 使用方法:")
    print("1. 在批量上传界面选择这些图片")
    print("2. 测试一体化的信息标注和处理配置功能")

if __name__ == "__main__":
    main()
