import java.util.HashMap;
import java.util.Map;

public class QuickTest {
    public static void main(String[] args) {
        try {
            // 检查poi-tl核心类是否可以加载
            Class<?> templateClass = Class.forName("com.deepoove.poi.XWPFTemplate");
            System.out.println("✅ poi-tl核心类加载成功: " + templateClass.getName());
            
            // 检查数据类
            Class<?> textsClass = Class.forName("com.deepoove.poi.data.Texts");
            System.out.println("✅ 数据处理类加载成功: " + textsClass.getName());
            
            // 检查Apache POI依赖
            Class<?> poiClass = Class.forName("org.apache.poi.xwpf.usermodel.XWPFDocument");
            System.out.println("✅ Apache POI依赖正常: " + poiClass.getName());
            
            System.out.println("\n🎉 poi-tl项目复现成功！");
            System.out.println("📁 项目位置: " + System.getProperty("user.dir"));
            System.out.println("🔧 可以开始使用poi-tl进行Word文档生成了");
            
        } catch (ClassNotFoundException e) {
            System.err.println("❌ 类加载失败: " + e.getMessage());
            System.err.println("请检查编译是否成功");
        }
    }
}
