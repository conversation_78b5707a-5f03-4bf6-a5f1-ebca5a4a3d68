# 15个球员模板创建指南

## 方法1：手动修改现有模板（推荐）

### 步骤1：复制现有模板
1. 复制 `template.docx` 为 `template_15players.docx`
2. 用Microsoft Word打开 `template_15players.docx`

### 步骤2：添加球员11-15的占位符
在现有的球员表格后面，添加以下标签：

#### 第三行球员（11-15号）
```
{{player11Number}}    {{player12Number}}    {{player13Number}}    {{player14Number}}    {{player15Number}}
{{@player11Photo}}    {{@player12Photo}}    {{@player13Photo}}    {{@player14Photo}}    {{@player15Photo}}
{{player11Name}}      {{player12Name}}      {{player13Name}}      {{player14Name}}      {{player15Name}}
```

### 步骤3：保存模板
保存为 `template_15players.docx`

## 方法2：修改配置文件使用新模板

修改 `config.properties` 文件：
```properties
# 改为使用15个球员的模板
template.path=template_15players.docx
```

## 需要添加的照片文件

在 `photos/` 目录中添加以下照片：
- `player11.jpg` 或 `player11.png`
- `player12.jpg` 或 `player12.png`
- `player13.jpg` 或 `player13.png`
- `player14.jpg` 或 `player14.png`
- `player15.jpg` 或 `player15.png`

## 完整的球员标签列表（1-15）

### 球员号码标签：
- {{player1Number}} 到 {{player15Number}}

### 球员照片标签：
- {{@player1Photo}} 到 {{@player15Photo}}

### 球员姓名标签：
- {{player1Name}} 到 {{player15Name}}

## 测试新模板

1. 添加缺失的照片文件
2. 修改配置文件使用新模板
3. 运行程序测试：`run-ai.bat`

## 注意事项

1. **标签格式**：必须严格按照 `{{playerXXNumber}}` 格式
2. **照片标签**：必须使用 `{{@playerXXPhoto}}` 格式（注意@符号）
3. **文件格式**：模板必须保存为 .docx 格式
4. **照片格式**：支持 PNG、JPG、JPEG 格式
