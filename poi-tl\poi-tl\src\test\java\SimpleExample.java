import java.util.HashMap;
import java.util.Map;
import java.io.File;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;

// 设置简单的日志配置
import org.slf4j.LoggerFactory;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.Level;

/**
 * 简单的poi-tl示例程序
 * 演示如何使用poi-tl生成Word文档
 */
public class SimpleExample {
    
    public static void main(String[] args) {
        try {
            // 设置日志级别为ERROR，减少日志输出
            Logger rootLogger = (Logger) LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
            rootLogger.setLevel(Level.ERROR);

            System.out.println("开始运行poi-tl示例程序...");

            // 创建数据模型
            Map<String, Object> data = new HashMap<>();
            
            // 简单的文本数据
            data.put("title", "poi-tl模板引擎演示");
            data.put("name", "poi-tl");
            data.put("description", "这是一个Java Word模板引擎");
            data.put("date", "2025-08-17");
            data.put("author", "开发者");
            
            // 使用一个简单的模板文件
            String templatePath = "src/test/resources/template/render_text.docx";
            File templateFile = new File(templatePath);
            if (!templateFile.exists()) {
                System.out.println("模板文件不存在: " + templatePath);
                System.out.println("程序退出");
                return;
            }
            
            System.out.println("使用模板文件: " + templatePath);
            
            // 生成Word文档
            String outputPath = "output_example.docx";
            XWPFTemplate.compile(templatePath)
                    .render(data)
                    .writeToFile(outputPath);
            
            System.out.println("Word文档生成成功！");
            System.out.println("输出文件: " + outputPath);
            System.out.println("请打开文件查看生成的内容。");
            
        } catch (Exception e) {
            System.err.println("程序运行出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
