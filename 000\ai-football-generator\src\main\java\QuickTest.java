import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.Pictures;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 快速测试AI功能的简单程序
 */
public class QuickTest {
    public static void main(String[] args) {
        try {
            System.out.println("🧪 开始快速测试...");

            // 使用原有项目的方法直接测试
            Map<String, Object> data = prepareTestData();

            String outputPath = "output/test_output_" + System.currentTimeMillis() + ".docx";

            try (FileInputStream templateStream = new FileInputStream("template.docx")) {
                XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);

                try (FileOutputStream outputStream = new FileOutputStream(outputPath)) {
                    template.write(outputStream);
                }

                template.close();

                System.out.println("✅ 测试成功！生成文件：" + outputPath);

            }

        } catch (Exception e) {
            System.err.println("❌ 测试出错：" + e.getMessage());
            e.printStackTrace();
        }
    }

    private static Map<String, Object> prepareTestData() {
        Map<String, Object> data = new HashMap<>();

        // 添加队伍基本信息
        data.put("title", "2025年五人制足球比赛报名表");
        data.put("organizationName", "太河镇人民政府");
        data.put("teamLeader", "李四");
        data.put("coach", "张三");
        data.put("teamDoctor", "王五");

        // 添加球员信息 - 使用原有项目的方法
        addPlayerData(data, 1, "10", "张雷", "photos/player1.png");
        addPlayerData(data, 2, "0", "白浩", "photos/player2.jpg");
        addPlayerData(data, 3, "3", "翟召昌", "photos/player3.jpg");
        addPlayerData(data, 4, "11", "赵飞", "photos/player4.jpg");
        addPlayerData(data, 5, "5", "王洪艺", "photos/player5.jpg");
        addPlayerData(data, 6, "6", "翟万峰", "photos/player6.jpg");
        addPlayerData(data, 7, "7", "马青海", "photos/player7.jpg");

        // 其余球员位置留空
        for (int i = 8; i <= 10; i++) {
            data.put("player" + i + "Number", "");
            data.put("player" + i + "Name", "");
        }

        return data;
    }

    /**
     * 添加单个球员数据 - 复制自原有项目
     */
    private static void addPlayerData(Map<String, Object> data, int playerIndex,
                                    String number, String name, String photoPath) {
        try {
            // 添加球员号码
            data.put("player" + playerIndex + "Number", number);

            // 添加球员姓名
            data.put("player" + playerIndex + "Name", name);

            // 处理球员照片 - 先裁剪再添加
            String processedPhotoPath = cropImageToSquare(photoPath, playerIndex);
            data.put("player" + playerIndex + "Photo",
                Pictures.ofLocal(processedPhotoPath)
                    .size(100, 120)  // 设置图片大小：宽100px，高120px
                    .create());

            System.out.println("✅ 添加球员数据：" + number + "号 " + name + " (照片: " + processedPhotoPath + ")");

        } catch (Exception e) {
            System.err.println("⚠️ 无法加载照片 " + photoPath + "，将跳过该球员的照片");
            // 如果照片加载失败，只添加文本信息
            data.put("player" + playerIndex + "Number", number);
            data.put("player" + playerIndex + "Name", name);
            // 不添加照片数据，模板中会显示占位符
        }
    }

    /**
     * 将图片裁剪为正方形（居中裁剪）- 复制自原有项目
     * 支持PNG和JPG格式，这样可以避免图片拉伸变形
     */
    private static String cropImageToSquare(String imagePath, int playerIndex) throws Exception {
        File imageFile = new File(imagePath);
        if (!imageFile.exists()) {
            throw new IOException("图片文件不存在: " + imagePath);
        }

        BufferedImage originalImage = ImageIO.read(imageFile);
        if (originalImage == null) {
            throw new IOException("无法读取图片: " + imagePath);
        }

        int width = originalImage.getWidth();
        int height = originalImage.getHeight();

        // 计算正方形的边长（取较小的尺寸）
        int size = Math.min(width, height);

        // 计算裁剪的起始位置（居中）
        int x = (width - size) / 2;
        int y = (height - size) / 2;

        // 裁剪图片为正方形
        BufferedImage croppedImage = originalImage.getSubimage(x, y, size, size);

        // 根据原图格式确定输出格式和扩展名
        String originalExtension = getFileExtension(imagePath);
        String outputFormat = originalExtension.equalsIgnoreCase("jpg") || originalExtension.equalsIgnoreCase("jpeg") ? "JPG" : "PNG";
        String outputExtension = outputFormat.toLowerCase();

        // 保存裁剪后的图片，保持原格式
        String croppedPath = "photos/player" + playerIndex + "_cropped." + outputExtension;
        ImageIO.write(croppedImage, outputFormat, new File(croppedPath));

        System.out.println("✂️ 图片已裁剪：" + imagePath + " -> " + croppedPath +
                          " (原尺寸: " + width + "x" + height + " -> 裁剪尺寸: " + size + "x" + size +
                          ", 格式: " + outputFormat + ")");
        return croppedPath;
    }

    /**
     * 获取文件扩展名 - 复制自原有项目
     */
    private static String getFileExtension(String filePath) {
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1);
        }
        return "";
    }
}
