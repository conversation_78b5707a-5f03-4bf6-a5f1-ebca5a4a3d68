/**
 * 球员数据模型
 */
public class PlayerData {
    private String number;      // 球衣号码
    private String name;        // 球员姓名
    private String photoPath;   // 照片路径
    
    public PlayerData() {}
    
    public PlayerData(String number, String name, String photoPath) {
        this.number = number;
        this.name = name;
        this.photoPath = photoPath;
    }
    
    // Getters and Setters
    public String getNumber() {
        return number;
    }
    
    public void setNumber(String number) {
        this.number = number;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getPhotoPath() {
        return photoPath;
    }
    
    public void setPhotoPath(String photoPath) {
        this.photoPath = photoPath;
    }
    
    @Override
    public String toString() {
        return String.format("球员[号码=%s, 姓名=%s, 照片=%s]", number, name, photoPath);
    }
}

/**
 * 队伍基本信息模型
 */
class TeamInfo {
    private String title;           // 比赛标题
    private String organizationName; // 单位名称
    private String teamLeader;      // 领队
    private String coach;           // 教练
    private String teamDoctor;      // 队医
    
    public TeamInfo() {}
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getOrganizationName() {
        return organizationName;
    }
    
    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }
    
    public String getTeamLeader() {
        return teamLeader;
    }
    
    public void setTeamLeader(String teamLeader) {
        this.teamLeader = teamLeader;
    }
    
    public String getCoach() {
        return coach;
    }
    
    public void setCoach(String coach) {
        this.coach = coach;
    }
    
    public String getTeamDoctor() {
        return teamDoctor;
    }
    
    public void setTeamDoctor(String teamDoctor) {
        this.teamDoctor = teamDoctor;
    }
    
    @Override
    public String toString() {
        return String.format("队伍信息[标题=%s, 单位=%s, 领队=%s, 教练=%s, 队医=%s]", 
                           title, organizationName, teamLeader, coach, teamDoctor);
    }
}

/**
 * 完整的足球队数据模型
 */
class FootballTeamData {
    private TeamInfo teamInfo;
    private PlayerData[] players;
    
    public FootballTeamData() {
        this.players = new PlayerData[10]; // 支持10个球员
    }
    
    public TeamInfo getTeamInfo() {
        return teamInfo;
    }
    
    public void setTeamInfo(TeamInfo teamInfo) {
        this.teamInfo = teamInfo;
    }
    
    public PlayerData[] getPlayers() {
        return players;
    }
    
    public void setPlayers(PlayerData[] players) {
        this.players = players;
    }
    
    public void setPlayer(int index, PlayerData player) {
        if (index >= 0 && index < players.length) {
            players[index] = player;
        }
    }
    
    public PlayerData getPlayer(int index) {
        if (index >= 0 && index < players.length) {
            return players[index];
        }
        return null;
    }
    
    public int getPlayerCount() {
        int count = 0;
        for (PlayerData player : players) {
            if (player != null) {
                count++;
            }
        }
        return count;
    }
}
