#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保守重试策略 - 单张处理，长间隔，多重试
Conservative Retry Strategy - Single processing, long intervals, multiple retries
"""

import os
import time
import requests
import json
from pathlib import Path
from PIL import Image
from config import *

def resize_image_conservative(image_path, target_size=(768, 1024)):
    """保守的图片调整策略"""
    original_path = Path(image_path)
    
    with Image.open(image_path) as img:
        width, height = img.size
        file_size_kb = os.path.getsize(image_path) / 1024
        
        print(f"📏 原始: {width}x{height}, {file_size_kb:.1f}KB")
        
        # 更保守的尺寸限制
        max_width, max_height = target_size
        
        if width <= max_width and height <= max_height and file_size_kb <= 150:
            print("✅ 图片已符合要求")
            return image_path
        
        # 计算缩放比例
        ratio = min(max_width / width, max_height / height)
        new_width = int(width * ratio)
        new_height = int(height * ratio)
        
        print(f"🔄 调整为: {new_width}x{new_height}")
        
        # 调整图片
        resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 保存路径
        resized_path = original_path.parent / f"{original_path.stem}_conservative{original_path.suffix}"
        
        # 保存时使用较高质量但控制文件大小
        resized_img.save(resized_path, format='JPEG', quality=80, optimize=True)
        
        new_size_kb = os.path.getsize(resized_path) / 1024
        print(f"✅ 保存为: {new_size_kb:.1f}KB")
        
        return str(resized_path)

def conservative_api_request(model_image, clothes_image, max_retries=3):
    """保守的API请求策略"""
    url = f"{BASE_URL}/302/comfyui/clothes-changer/create-task"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "User-Agent": "Fashion-Tryon-Toolkit/1.0"
    }
    
    for attempt in range(1, max_retries + 1):
        print(f"🔄 API请求尝试 {attempt}/{max_retries}")
        
        try:
            with open(model_image, 'rb') as model_file, open(clothes_image, 'rb') as clothes_file:
                files = {
                    'modelImageFile': (os.path.basename(model_image), model_file, 'image/jpeg'),
                    'clothesImageFile': (os.path.basename(clothes_image), clothes_file, 'image/jpeg')
                }
                
                data = {
                    "modelImgSegLabels": "10",
                    "clothesImgSegLabels": "10"
                }
                
                # 增加超时时间
                response = requests.post(
                    url, 
                    headers=headers, 
                    files=files, 
                    data=data, 
                    timeout=60  # 增加到60秒
                )
                
                print(f"📊 响应状态: {response.status_code}")
                
                if response.status_code == 201:
                    response_data = response.json()
                    
                    # 多种方式获取taskId
                    task_id = response_data.get('taskId')
                    if not task_id and 'data' in response_data:
                        task_id = response_data['data'].get('taskId')
                    
                    if task_id:
                        print(f"✅ 任务创建成功: {task_id}")
                        return task_id
                    else:
                        print(f"❌ 未找到taskId: {response_data}")
                        
                else:
                    print(f"❌ 请求失败: {response.status_code}")
                    print(f"📄 响应: {response.text}")
                
        except requests.exceptions.Timeout:
            print(f"❌ 请求超时 (尝试 {attempt}/{max_retries})")
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
        
        if attempt < max_retries:
            wait_time = 30 * attempt  # 递增等待时间
            print(f"⏳ 等待 {wait_time} 秒后重试...")
            time.sleep(wait_time)
    
    return None

def conservative_wait_for_task(task_id, max_wait_minutes=15):
    """保守的任务等待策略"""
    url = f"{BASE_URL}/302/comfyui/clothes-changer/query-task"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "User-Agent": "Fashion-Tryon-Toolkit/1.0"
    }
    
    max_attempts = max_wait_minutes * 2  # 每30秒查询一次
    
    for attempt in range(1, max_attempts + 1):
        try:
            print(f"🔍 查询进度 {attempt}/{max_attempts}")
            
            response = requests.get(
                url,
                headers=headers,
                params={"taskId": task_id},
                timeout=30
            )
            
            if response.status_code == 200:
                task_data = response.json()
                status = task_data.get('status', 'UNKNOWN')
                print(f"📈 状态: {status}")
                
                if status == 'SUCCESS':
                    result_url = task_data.get('resultUrl')
                    if result_url:
                        print(f"🎉 任务完成: {result_url}")
                        return result_url
                    else:
                        print(f"❌ 成功但无结果URL")
                        return None
                        
                elif status == 'FAILED':
                    print(f"❌ 任务失败")
                    print(f"📄 详情: {json.dumps(task_data, indent=2, ensure_ascii=False)}")
                    return None
                    
                elif status in ['SUBMITTING', 'RUNNING']:
                    print(f"⏳ 进行中，等待30秒...")
                    time.sleep(30)
                    continue
                else:
                    print(f"❓ 未知状态: {status}")
                    time.sleep(30)
                    continue
                    
            else:
                print(f"❌ 查询失败: {response.status_code}")
                time.sleep(30)
                continue
                
        except Exception as e:
            print(f"❌ 查询异常: {str(e)}")
            time.sleep(30)
            continue
    
    print(f"❌ 任务超时 ({max_wait_minutes}分钟)")
    return None

def test_single_photo_conservative(photo_path, clothes_image):
    """保守策略测试单张照片"""
    photo_name = Path(photo_path).stem
    
    print("=" * 80)
    print(f"🧪 保守策略测试: {photo_name}")
    print("=" * 80)
    
    try:
        # 步骤1: 预处理图片
        print("🔧 步骤1: 图片预处理")
        processed_photo = resize_image_conservative(photo_path)
        
        # 步骤2: API请求
        print("\n📡 步骤2: API请求")
        task_id = conservative_api_request(processed_photo, clothes_image)
        
        if not task_id:
            print("❌ API请求失败")
            return False
        
        # 步骤3: 等待结果
        print("\n⏳ 步骤3: 等待结果")
        result_url = conservative_wait_for_task(task_id)
        
        if result_url:
            print("✅ 测试成功！")
            return True
        else:
            print("❌ 任务失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def main():
    """主函数 - 保守策略重试"""
    clothes_image = "1111/微信图片_20250816210622_26_328.jpg"
    
    # 只测试一张最有希望成功的照片
    test_photo = "1111/微信图片_2025-08-19_095124_909.jpg"  # 这张之前API连接成功了
    
    print("🐌 保守策略重试 - 单张测试")
    print("=" * 80)
    
    # 创建输出目录
    for dir_name in OUTPUT_DIRS.values():
        os.makedirs(dir_name, exist_ok=True)
    
    success = test_single_photo_conservative(test_photo, clothes_image)
    
    if success:
        print("\n🎉 保守策略成功！可以继续处理其他照片")
    else:
        print("\n😞 保守策略仍然失败，可能需要检查:")
        print("   1. 网络连接稳定性")
        print("   2. API服务状态")
        print("   3. 账户余额和限制")

if __name__ == "__main__":
    main()
