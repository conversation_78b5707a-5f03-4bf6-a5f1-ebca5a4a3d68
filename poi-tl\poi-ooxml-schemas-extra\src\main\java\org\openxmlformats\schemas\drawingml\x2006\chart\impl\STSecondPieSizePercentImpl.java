/*
 * XML Type:  ST_SecondPieSizePercent
 * Namespace: http://schemas.openxmlformats.org/drawingml/2006/chart
 * Java type: org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizePercent
 *
 * Automatically generated - do not modify.
 */
package org.openxmlformats.schemas.drawingml.x2006.chart.impl;

import javax.xml.namespace.QName;
import org.apache.xmlbeans.QNameSet;

/**
 * An XML ST_SecondPieSizePercent(@http://schemas.openxmlformats.org/drawingml/2006/chart).
 *
 * This is an atomic type that is a restriction of org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizePercent.
 */
public class STSecondPieSizePercentImpl extends org.apache.xmlbeans.impl.values.JavaStringHolderEx implements org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizePercent {
    private static final long serialVersionUID = 1L;

    public STSecondPieSizePercentImpl(org.apache.xmlbeans.SchemaType sType) {
        super(sType, false);
    }

    protected STSecondPieSizePercentImpl(org.apache.xmlbeans.SchemaType sType, boolean b) {
        super(sType, b);
    }
}
