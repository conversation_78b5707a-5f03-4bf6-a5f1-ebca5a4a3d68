import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Pictures;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 批量结果处理器
 * 将batch_results文件夹中的照片应用到Word模板中
 */
public class BatchResultsProcessor {
    
    private static final String BATCH_RESULTS_DIR = "batch_results/";
    private static final String TEMPLATE_PATH = "template_15players.docx";
    private static final String OUTPUT_DIR = "output/";
    
    public static void main(String[] args) {
        try {
            System.out.println("🚀 开始处理batch_results中的照片...");
            
            BatchResultsProcessor processor = new BatchResultsProcessor();
            processor.processBatchResults();
            
        } catch (Exception e) {
            System.err.println("❌ 处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理batch_results中的照片并生成Word文档
     */
    public void processBatchResults() throws IOException {
        // 1. 检查必要的文件和目录
        if (!checkRequiredFiles()) {
            return;
        }
        
        // 2. 准备模板数据
        Map<String, Object> data = prepareTemplateData();
        
        // 3. 生成Word文档
        generateWordDocument(data);
    }
    
    /**
     * 检查必要的文件和目录
     */
    private boolean checkRequiredFiles() {
        // 检查batch_results目录
        Path batchResultsPath = Paths.get(BATCH_RESULTS_DIR);
        if (!Files.exists(batchResultsPath)) {
            System.err.println("❌ batch_results目录不存在: " + BATCH_RESULTS_DIR);
            return false;
        }
        
        // 检查模板文件
        Path templatePath = Paths.get(TEMPLATE_PATH);
        if (!Files.exists(templatePath)) {
            System.err.println("❌ 模板文件不存在: " + TEMPLATE_PATH);
            return false;
        }
        
        // 创建输出目录
        Path outputPath = Paths.get(OUTPUT_DIR);
        if (!Files.exists(outputPath)) {
            try {
                Files.createDirectories(outputPath);
                System.out.println("✅ 创建输出目录: " + OUTPUT_DIR);
            } catch (IOException e) {
                System.err.println("❌ 无法创建输出目录: " + e.getMessage());
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 准备模板数据
     */
    private Map<String, Object> prepareTemplateData() throws IOException {
        Map<String, Object> data = new HashMap<>();

        // 设置基本信息
        data.put("title", "AI足球队报名表");
        data.put("organizationName", "AI足球俱乐部");
        data.put("teamLeader", "领队");
        data.put("coach", "教练");
        data.put("teamDoctor", "队医");

        // 处理球员照片（player01_final.png 到 player15_final.png）
        for (int i = 1; i <= 15; i++) {
            String playerNumber = String.format("%02d", i);
            String photoFileName = "player" + playerNumber + "_final.png";
            String photoPath = BATCH_RESULTS_DIR + photoFileName;

            // 设置球员号码和姓名
            data.put("player" + i + "Number", String.valueOf(i));
            data.put("player" + i + "Name", "球员" + i);

            // 检查照片是否存在
            Path photoFilePath = Paths.get(photoPath);
            if (Files.exists(photoFilePath)) {
                try {
                    // 裁剪并优化照片
                    String processedPhotoPath = cropAndOptimizePhoto(photoPath, i);

                    // 添加照片到模板数据
                    data.put("player" + i + "Photo",
                        Pictures.ofLocal(processedPhotoPath)
                            .size(120, 150)  // 增大图片尺寸，改善显示效果
                            .create());
                    System.out.println("✅ 添加球员" + i + "照片: " + processedPhotoPath);
                } catch (Exception e) {
                    System.err.println("⚠️  球员" + i + "照片处理失败: " + e.getMessage());
                    // 如果照片处理失败，尝试使用原图
                    try {
                        data.put("player" + i + "Photo",
                            Pictures.ofLocal(photoPath)
                                .size(120, 150)
                                .create());
                        System.out.println("⚠️  使用原图: " + photoPath);
                    } catch (Exception e2) {
                        System.err.println("❌ 球员" + i + "照片完全无法加载");
                    }
                }
            } else {
                System.out.println("⚠️  球员" + i + "照片不存在: " + photoPath);
                // 对于不存在的照片，不添加照片数据，模板会显示空白
            }
        }

        return data;
    }

    /**
     * 裁剪并优化照片
     */
    private String cropAndOptimizePhoto(String originalPhotoPath, int playerIndex) throws IOException {
        // 创建处理后的照片目录
        Path processedDir = Paths.get("batch_results/processed/");
        if (!Files.exists(processedDir)) {
            Files.createDirectories(processedDir);
        }

        // 读取原始图片
        BufferedImage originalImage = ImageIO.read(new File(originalPhotoPath));
        if (originalImage == null) {
            throw new IOException("无法读取图片: " + originalPhotoPath);
        }

        // 裁剪为正方形（居中裁剪）
        BufferedImage croppedImage = cropToSquare(originalImage);

        // 调整大小为标准尺寸
        BufferedImage resizedImage = resizeImage(croppedImage, 300, 300);

        // 保存处理后的图片
        String processedFileName = "player" + String.format("%02d", playerIndex) + "_processed.png";
        String processedPath = "batch_results/processed/" + processedFileName;

        ImageIO.write(resizedImage, "PNG", new File(processedPath));

        return processedPath;
    }

    /**
     * 将图片裁剪为正方形（居中裁剪）
     */
    private BufferedImage cropToSquare(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 计算正方形的边长（取较小的边）
        int size = Math.min(width, height);

        // 计算裁剪的起始位置（居中）
        int x = (width - size) / 2;
        int y = (height - size) / 2;

        // 裁剪图片
        return image.getSubimage(x, y, size, size);
    }

    /**
     * 调整图片大小
     */
    private BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resizedImage.createGraphics();

        // 设置高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制调整后的图片
        g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();

        return resizedImage;
    }

    /**
     * 生成Word文档
     */
    private void generateWordDocument(Map<String, Object> data) throws IOException {
        String outputFileName = "batch_results_report_" + System.currentTimeMillis() + ".docx";
        String outputPath = OUTPUT_DIR + outputFileName;
        
        try (FileInputStream templateStream = new FileInputStream(TEMPLATE_PATH)) {
            XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);
            
            try (FileOutputStream outputStream = new FileOutputStream(outputPath)) {
                template.write(outputStream);
            }
            
            template.close();
            
            System.out.println("🎉 Word文档生成成功！");
            System.out.println("📄 文件位置: " + outputPath);
            
        } catch (Exception e) {
            System.err.println("❌ Word文档生成失败: " + e.getMessage());
            throw new IOException("Word文档生成失败", e);
        }
    }
}
