import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Pictures;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 批量结果处理器
 * 将batch_results文件夹中的照片应用到Word模板中
 */
public class BatchResultsProcessor {
    
    private static final String BATCH_RESULTS_DIR = "batch_results/";
    private static final String TEMPLATE_PATH = "template_15players.docx";
    private static final String OUTPUT_DIR = "output/";
    
    public static void main(String[] args) {
        try {
            System.out.println("🚀 开始处理batch_results中的照片...");
            
            BatchResultsProcessor processor = new BatchResultsProcessor();
            processor.processBatchResults();
            
        } catch (Exception e) {
            System.err.println("❌ 处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理batch_results中的照片并生成Word文档
     */
    public void processBatchResults() throws IOException {
        // 1. 检查必要的文件和目录
        if (!checkRequiredFiles()) {
            return;
        }
        
        // 2. 准备模板数据
        Map<String, Object> data = prepareTemplateData();
        
        // 3. 生成Word文档
        generateWordDocument(data);
    }
    
    /**
     * 检查必要的文件和目录
     */
    private boolean checkRequiredFiles() {
        // 检查batch_results目录
        Path batchResultsPath = Paths.get(BATCH_RESULTS_DIR);
        if (!Files.exists(batchResultsPath)) {
            System.err.println("❌ batch_results目录不存在: " + BATCH_RESULTS_DIR);
            return false;
        }
        
        // 检查模板文件
        Path templatePath = Paths.get(TEMPLATE_PATH);
        if (!Files.exists(templatePath)) {
            System.err.println("❌ 模板文件不存在: " + TEMPLATE_PATH);
            return false;
        }
        
        // 创建输出目录
        Path outputPath = Paths.get(OUTPUT_DIR);
        if (!Files.exists(outputPath)) {
            try {
                Files.createDirectories(outputPath);
                System.out.println("✅ 创建输出目录: " + OUTPUT_DIR);
            } catch (IOException e) {
                System.err.println("❌ 无法创建输出目录: " + e.getMessage());
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 准备模板数据
     */
    private Map<String, Object> prepareTemplateData() throws IOException {
        Map<String, Object> data = new HashMap<>();

        // 设置基本信息
        data.put("title", "AI足球队报名表");
        data.put("organizationName", "AI足球俱乐部");
        data.put("teamLeader", "领队");
        data.put("coach", "教练");
        data.put("teamDoctor", "队医");

        // 处理球员照片（player01_final.png 到 player15_final.png）
        for (int i = 1; i <= 15; i++) {
            String playerNumber = String.format("%02d", i);
            String photoFileName = "player" + playerNumber + "_final.png";
            String photoPath = BATCH_RESULTS_DIR + photoFileName;

            // 设置球员号码和姓名
            data.put("player" + i + "Number", String.valueOf(i));
            data.put("player" + i + "Name", "球员" + i);

            // 检查照片是否存在
            Path photoFilePath = Paths.get(photoPath);
            if (Files.exists(photoFilePath)) {
                try {
                    // 裁剪并优化照片
                    String processedPhotoPath = cropAndOptimizePhoto(photoPath, i);

                    // 添加照片到模板数据（调整显示尺寸以保持清晰度）
                    data.put("player" + i + "Photo",
                        Pictures.ofLocal(processedPhotoPath)
                            .size(96, 120)  // 4:5比例，适中大小，保持清晰度
                            .create());
                    System.out.println("✅ 添加球员" + i + "照片: " + processedPhotoPath);
                } catch (Exception e) {
                    System.err.println("⚠️  球员" + i + "照片处理失败: " + e.getMessage());
                    // 如果照片处理失败，尝试使用原图
                    try {
                        data.put("player" + i + "Photo",
                            Pictures.ofLocal(photoPath)
                                .size(80, 100)
                                .create());
                        System.out.println("⚠️  使用原图: " + photoPath);
                    } catch (Exception e2) {
                        System.err.println("❌ 球员" + i + "照片完全无法加载");
                    }
                }
            } else {
                System.out.println("⚠️  球员" + i + "照片不存在: " + photoPath);
                // 对于不存在的照片，不添加照片数据，模板会显示空白
            }
        }

        return data;
    }

    /**
     * 裁剪并优化照片
     */
    private String cropAndOptimizePhoto(String originalPhotoPath, int playerIndex) throws IOException {
        // 创建处理后的照片目录
        Path processedDir = Paths.get("batch_results/processed/");
        if (!Files.exists(processedDir)) {
            Files.createDirectories(processedDir);
        }

        // 读取原始图片
        BufferedImage originalImage = ImageIO.read(new File(originalPhotoPath));
        if (originalImage == null) {
            throw new IOException("无法读取图片: " + originalPhotoPath);
        }

        // 智能裁剪人物照片（保留上半身）
        BufferedImage croppedImage = cropToSquare(originalImage);

        // 调整大小为标准尺寸（4:5比例，更适合人物照片）
        BufferedImage resizedImage = resizeImage(croppedImage, 240, 300);

        // 保存处理后的图片
        String processedFileName = "player" + String.format("%02d", playerIndex) + "_processed.png";
        String processedPath = "batch_results/processed/" + processedFileName;

        ImageIO.write(resizedImage, "PNG", new File(processedPath));

        return processedPath;
    }

    /**
     * 智能裁剪人物照片 - 优先保留上半身
     */
    private BufferedImage cropToSquare(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 目标是4:5的比例（宽:高），更适合人物照片
        int targetWidth, targetHeight;

        if (width > height) {
            // 横向图片：以高度为基准
            targetHeight = height;
            targetWidth = (int)(height * 0.8); // 4:5比例
        } else {
            // 纵向图片：智能裁剪上半身
            targetWidth = width;
            targetHeight = (int)(width * 1.25); // 4:5比例

            // 如果计算出的高度超过原图，则使用原图高度
            if (targetHeight > height) {
                targetHeight = height;
                targetWidth = (int)(height * 0.8);
            }
        }

        // 计算裁剪位置
        int x = (width - targetWidth) / 2;  // 水平居中

        // 垂直位置：对于人物照片，偏向上方（保留头部和上半身）
        int y;
        if (height > width * 1.5) {
            // 全身照：从上方1/4处开始裁剪，保留头部和上半身
            y = height / 4;
        } else {
            // 半身照或头像：稍微偏上
            y = Math.max(0, (height - targetHeight) / 3);
        }

        // 确保裁剪区域不超出图片边界
        x = Math.max(0, Math.min(x, width - targetWidth));
        y = Math.max(0, Math.min(y, height - targetHeight));

        // 裁剪图片
        return image.getSubimage(x, y, targetWidth, targetHeight);
    }

    /**
     * 调整图片大小
     */
    private BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resizedImage.createGraphics();

        // 设置高质量渲染
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // 绘制调整后的图片
        g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();

        return resizedImage;
    }

    /**
     * 生成Word文档
     */
    private void generateWordDocument(Map<String, Object> data) throws IOException {
        String outputFileName = "batch_results_report_" + System.currentTimeMillis() + ".docx";
        String outputPath = OUTPUT_DIR + outputFileName;
        
        try (FileInputStream templateStream = new FileInputStream(TEMPLATE_PATH)) {
            XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);
            
            try (FileOutputStream outputStream = new FileOutputStream(outputPath)) {
                template.write(outputStream);
            }
            
            template.close();
            
            System.out.println("🎉 Word文档生成成功！");
            System.out.println("📄 文件位置: " + outputPath);
            
        } catch (Exception e) {
            System.err.println("❌ Word文档生成失败: " + e.getMessage());
            throw new IOException("Word文档生成失败", e);
        }
    }
}
