import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Pictures;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 批量结果处理器
 * 将batch_results文件夹中的照片应用到Word模板中
 */
public class BatchResultsProcessor {
    
    private static final String BATCH_RESULTS_DIR = "batch_results/";
    private static final String TEMPLATE_PATH = "template_15players.docx";
    private static final String OUTPUT_DIR = "output/";
    
    public static void main(String[] args) {
        try {
            System.out.println("🚀 开始处理batch_results中的照片...");
            
            BatchResultsProcessor processor = new BatchResultsProcessor();
            processor.processBatchResults();
            
        } catch (Exception e) {
            System.err.println("❌ 处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理batch_results中的照片并生成Word文档
     */
    public void processBatchResults() throws IOException {
        // 1. 检查必要的文件和目录
        if (!checkRequiredFiles()) {
            return;
        }
        
        // 2. 准备模板数据
        Map<String, Object> data = prepareTemplateData();
        
        // 3. 生成Word文档
        generateWordDocument(data);
    }
    
    /**
     * 检查必要的文件和目录
     */
    private boolean checkRequiredFiles() {
        // 检查batch_results目录
        Path batchResultsPath = Paths.get(BATCH_RESULTS_DIR);
        if (!Files.exists(batchResultsPath)) {
            System.err.println("❌ batch_results目录不存在: " + BATCH_RESULTS_DIR);
            return false;
        }
        
        // 检查模板文件
        Path templatePath = Paths.get(TEMPLATE_PATH);
        if (!Files.exists(templatePath)) {
            System.err.println("❌ 模板文件不存在: " + TEMPLATE_PATH);
            return false;
        }
        
        // 创建输出目录
        Path outputPath = Paths.get(OUTPUT_DIR);
        if (!Files.exists(outputPath)) {
            try {
                Files.createDirectories(outputPath);
                System.out.println("✅ 创建输出目录: " + OUTPUT_DIR);
            } catch (IOException e) {
                System.err.println("❌ 无法创建输出目录: " + e.getMessage());
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 准备模板数据
     */
    private Map<String, Object> prepareTemplateData() throws IOException {
        Map<String, Object> data = new HashMap<>();
        
        // 设置基本信息
        data.put("title", "AI足球队报名表");
        data.put("organizationName", "AI足球俱乐部");
        data.put("teamLeader", "领队");
        data.put("coach", "教练");
        data.put("teamDoctor", "队医");
        
        // 处理球员照片（player01_final.png 到 player13_final.png）
        for (int i = 1; i <= 15; i++) {
            String playerNumber = String.format("%02d", i);
            String photoFileName = "player" + playerNumber + "_final.png";
            String photoPath = BATCH_RESULTS_DIR + photoFileName;
            
            // 设置球员号码和姓名
            data.put("player" + i + "Number", String.valueOf(i));
            data.put("player" + i + "Name", "球员" + i);
            
            // 检查照片是否存在
            Path photoFilePath = Paths.get(photoPath);
            if (Files.exists(photoFilePath)) {
                try {
                    // 添加照片到模板数据
                    data.put("player" + i + "Photo", 
                        Pictures.ofLocal(photoPath)
                            .size(100, 120)  // 设置图片大小
                            .create());
                    System.out.println("✅ 添加球员" + i + "照片: " + photoPath);
                } catch (Exception e) {
                    System.err.println("⚠️  球员" + i + "照片加载失败: " + e.getMessage());
                    // 如果照片加载失败，不添加照片数据
                }
            } else {
                System.out.println("⚠️  球员" + i + "照片不存在: " + photoPath);
                // 对于不存在的照片，不添加照片数据，模板会显示空白
            }
        }
        
        return data;
    }
    
    /**
     * 生成Word文档
     */
    private void generateWordDocument(Map<String, Object> data) throws IOException {
        String outputFileName = "batch_results_report_" + System.currentTimeMillis() + ".docx";
        String outputPath = OUTPUT_DIR + outputFileName;
        
        try (FileInputStream templateStream = new FileInputStream(TEMPLATE_PATH)) {
            XWPFTemplate template = XWPFTemplate.compile(templateStream).render(data);
            
            try (FileOutputStream outputStream = new FileOutputStream(outputPath)) {
                template.write(outputStream);
            }
            
            template.close();
            
            System.out.println("🎉 Word文档生成成功！");
            System.out.println("📄 文件位置: " + outputPath);
            
        } catch (Exception e) {
            System.err.println("❌ Word文档生成失败: " + e.getMessage());
            throw new IOException("Word文档生成失败", e);
        }
    }
}
