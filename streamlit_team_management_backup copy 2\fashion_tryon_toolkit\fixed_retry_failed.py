#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版重试失败照片 - 应用所有发现的修复
Fixed Retry Failed Photos - Apply all discovered fixes
"""

import os
import time
from pathlib import Path
from batch_fashion_tryon import process_single_photo, create_output_dirs
from config import get_total_cost_per_image, get_total_cost_cny_per_image

def main():
    """主函数 - 重试失败的3张照片"""
    # 创建输出目录
    create_output_dirs()
    
    # 服装模板
    clothes_image = "1111/微信图片_20250816210622_26_328.jpg"
    
    # 失败的3张照片
    failed_photos = [
        "1111/微信图片_2025-08-19_095051_534.jpg",
        "1111/微信图片_2025-08-19_095124_909.jpg", 
        "1111/微信图片_2025-08-19_095132_429.jpg"
    ]
    
    print("=" * 100)
    print("🔧 修复版重试失败照片")
    print("=" * 100)
    print("🛠️  应用的修复:")
    print("   ✅ 状态码处理: 支持200和201")
    print("   ✅ API端点: 使用正确的check-task-status")
    print("   ✅ 响应解析: 正确解析data和output字段")
    print("   ✅ 超时时间: 增加到20分钟")
    print("   ✅ 重试次数: 增加到40次")
    print()
    print(f"📊 重试照片: {len(failed_photos)} 张")
    print(f"👕 服装模板: {clothes_image}")
    print(f"💰 预估成本: {len(failed_photos) * get_total_cost_per_image()} PTC (约{len(failed_photos) * get_total_cost_cny_per_image():.1f}元)")
    print()
    
    # 显示要处理的文件列表
    print("📋 重试列表:")
    for i, photo in enumerate(failed_photos, 1):
        photo_name = Path(photo).name
        if os.path.exists(photo):
            print(f"   ✅ {i}. {photo_name}")
        else:
            print(f"   ❌ {i}. {photo_name} (文件不存在)")
    print()
    
    # 确认处理
    confirm = input("🤔 确认开始重试吗？(y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消重试")
        return
    
    # 处理每张照片
    success_count = 0
    results = []
    
    for i, photo_path in enumerate(failed_photos, 1):
        photo_name = Path(photo_path).stem
        print(f"\n🔄 重试进度: {i}/{len(failed_photos)} - {photo_name}")
        print("=" * 80)
        
        if not os.path.exists(photo_path):
            print(f"❌ 文件不存在，跳过: {photo_path}")
            results.append({"photo": photo_name, "success": False, "reason": "文件不存在"})
            continue
        
        try:
            start_time = time.time()
            result = process_single_photo(photo_path, clothes_image, photo_name)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            if result.get("success", False):
                success_count += 1
                print(f"✅ {photo_name} 重试成功！耗时: {processing_time:.1f}秒")
                results.append({
                    "photo": photo_name, 
                    "success": True, 
                    "time": processing_time,
                    "result": result
                })
            else:
                print(f"❌ {photo_name} 重试失败")
                results.append({
                    "photo": photo_name, 
                    "success": False, 
                    "reason": result.get("error", "未知错误"),
                    "time": processing_time
                })
                
        except Exception as e:
            print(f"❌ {photo_name} 处理异常: {str(e)}")
            results.append({
                "photo": photo_name, 
                "success": False, 
                "reason": f"异常: {str(e)}"
            })
        
        # 成功处理后等待一下，避免API限制
        if i < len(failed_photos):
            wait_time = 60 if success_count > 0 else 30
            print(f"\n⏳ 等待 {wait_time} 秒后处理下一张...")
            time.sleep(wait_time)
    
    # 显示最终结果
    print("\n" + "=" * 100)
    print("🎉 重试完成！")
    print("=" * 100)
    print(f"📊 重试统计:")
    print(f"   ✅ 成功: {success_count} 张")
    print(f"   ❌ 失败: {len(failed_photos) - success_count} 张")
    print(f"   📈 成功率: {success_count/len(failed_photos)*100:.1f}%")
    
    if success_count > 0:
        actual_cost = success_count * get_total_cost_per_image()
        actual_cost_cny = success_count * get_total_cost_cny_per_image()
        print(f"   💰 实际成本: {actual_cost} PTC (约{actual_cost_cny:.1f}元)")
    
    print()
    print("📋 详细结果:")
    for i, result in enumerate(results, 1):
        if result["success"]:
            time_str = f" ({result['time']:.1f}秒)" if 'time' in result else ""
            print(f"   ✅ {i}. {result['photo']}{time_str}")
        else:
            reason = result.get('reason', '未知')
            print(f"   ❌ {i}. {result['photo']} - {reason}")
    
    if success_count > 0:
        print(f"\n🎯 成功的照片已保存到 batch_results/ 目录")
        print("💡 提示: 可以查看生成的白底背景图片效果")
    
    if success_count < len(failed_photos):
        print(f"\n🤔 仍有 {len(failed_photos) - success_count} 张照片失败")
        print("💡 建议:")
        print("   1. 检查网络连接稳定性")
        print("   2. 稍后再次重试")
        print("   3. 考虑调整图片尺寸")
    
    print("=" * 100)

if __name__ == "__main__":
    main()
