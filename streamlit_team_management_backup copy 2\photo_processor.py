#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球队管理系统 - 照片处理模块
Team Management System - Photo Processing Module

集成AI照片处理功能：换装、背景去除、添加白底
"""

import os
import asyncio
import aiohttp
import aiofiles
import time
import json
from datetime import datetime
from PIL import Image
import concurrent.futures
import threading
from pathlib import Path

# 照片处理配置
PHOTO_PROCESS_CONFIG = {
    # API配置
    "api_key": "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o",
    "base_url": "https://api.302.ai",
    
    # 处理参数
    "fashion_tryon": {
        "modelImgSegLabels": "10",    # 10-上衣, 5-裤子, 6-裙子
        "clothesImgSegLabels": "10"   # 10-上衣, 5-裤子, 6-裙子
    },
    
    # 超时设置
    "timeout": {
        "request_timeout": 30,
        "task_max_wait": 600,
        "task_check_interval": 30,
        "max_retry_attempts": 20
    },
    
    # 成本配置
    "costs": {
        "fashion_tryon": 0.1,
        "remove_background": 0.5,
        "white_background": 0.0
    },
    
    # 白底背景配置
    "white_background": {
        "background_color": "white",
        "output_format": "PNG",
        "dpi": 300
    }
}

# 全局锁用于线程安全的PIL操作
pil_lock = threading.Lock()

class TeamPhotoProcessor:
    """球队照片处理器"""
    
    def __init__(self, team_name, output_folder="processed_photos"):
        self.team_name = team_name
        self.output_folder = output_folder
        self.session = None
        
        # 创建输出目录
        self.team_output_dir = os.path.join(output_folder, team_name)
        os.makedirs(self.team_output_dir, exist_ok=True)
        
        # 创建子目录
        for subdir in ["fashion", "no_background", "white_background", "temp"]:
            os.makedirs(os.path.join(self.team_output_dir, subdir), exist_ok=True)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        timeout = aiohttp.ClientTimeout(total=PHOTO_PROCESS_CONFIG["timeout"]["request_timeout"])
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def process_player_photo(self, player_info, photo_path, clothes_image_path=None, 
                                 process_fashion=False, process_background=False, process_white=False):
        """处理单个球员照片"""
        player_name = player_info['name']
        player_id = player_info['id']
        
        print(f"🎯 开始处理球员: {player_name}")
        
        result = {
            "player_info": player_info,
            "original_photo": photo_path,
            "start_time": datetime.now().isoformat(),
            "steps": {},
            "success": False,
            "final_results": {},
            "processing_time": 0,
            "total_cost": 0
        }
        
        start_time = time.time()
        current_image = photo_path
        
        try:
            # 步骤1: 换装处理
            if process_fashion and clothes_image_path:
                fashion_result = await self.fashion_tryon(current_image, clothes_image_path, player_name, player_id)
                result["steps"]["fashion_tryon"] = {
                    "success": fashion_result is not None,
                    "result_path": fashion_result,
                    "cost": PHOTO_PROCESS_CONFIG["costs"]["fashion_tryon"] if fashion_result else 0
                }
                
                if fashion_result:
                    current_image = fashion_result
                    result["final_results"]["fashion"] = fashion_result
                    print(f"✅ [{player_name}] 换装完成")
                else:
                    print(f"❌ [{player_name}] 换装失败")
            
            # 步骤2: 背景去除
            if process_background:
                bg_remove_result = await self.remove_background(current_image, player_name, player_id)
                result["steps"]["remove_background"] = {
                    "success": bg_remove_result is not None,
                    "result_path": bg_remove_result,
                    "cost": PHOTO_PROCESS_CONFIG["costs"]["remove_background"] if bg_remove_result else 0
                }
                
                if bg_remove_result:
                    current_image = bg_remove_result
                    result["final_results"]["no_background"] = bg_remove_result
                    print(f"✅ [{player_name}] 背景去除完成")
                else:
                    print(f"❌ [{player_name}] 背景去除失败")
            
            # 步骤3: 添加白底
            if process_white:
                white_bg_result = await self.add_white_background(current_image, player_name, player_id)
                result["steps"]["white_background"] = {
                    "success": white_bg_result is not None,
                    "result_path": white_bg_result,
                    "cost": PHOTO_PROCESS_CONFIG["costs"]["white_background"]
                }
                
                if white_bg_result:
                    result["final_results"]["white_background"] = white_bg_result
                    print(f"✅ [{player_name}] 白底添加完成")
                else:
                    print(f"❌ [{player_name}] 白底添加失败")
            
            # 计算总成本和时间
            result["total_cost"] = sum(step.get("cost", 0) for step in result["steps"].values())
            result["processing_time"] = time.time() - start_time
            result["end_time"] = datetime.now().isoformat()
            result["success"] = len(result["final_results"]) > 0
            
            return result
            
        except Exception as e:
            print(f"❌ [{player_name}] 处理异常: {e}")
            result["processing_time"] = time.time() - start_time
            result["error"] = str(e)
            return result
    
    async def fashion_tryon(self, model_image, clothes_image, player_name, player_id):
        """AI换装处理"""
        print(f"🎯 [{player_name}] 开始换装处理")
        
        if not os.path.exists(model_image) or not os.path.exists(clothes_image):
            print(f"❌ [{player_name}] 图片文件不存在")
            return None
        
        url = f"{PHOTO_PROCESS_CONFIG['base_url']}/302/comfyui/clothes-changer/create-task"
        headers = {"Authorization": f"Bearer {PHOTO_PROCESS_CONFIG['api_key']}"}
        
        try:
            async with aiofiles.open(model_image, 'rb') as model_file, \
                       aiofiles.open(clothes_image, 'rb') as clothes_file:
                
                model_content = await model_file.read()
                clothes_content = await clothes_file.read()
                
                data = aiohttp.FormData()
                data.add_field('modelImageFile', model_content, 
                              filename=os.path.basename(model_image), 
                              content_type='image/jpeg')
                data.add_field('clothesImageFile', clothes_content,
                              filename=os.path.basename(clothes_image),
                              content_type='image/png')
                data.add_field('modelImgSegLabels', PHOTO_PROCESS_CONFIG["fashion_tryon"]["modelImgSegLabels"])
                data.add_field('clothesImgSegLabels', PHOTO_PROCESS_CONFIG["fashion_tryon"]["clothesImgSegLabels"])
                
                async with self.session.post(url, headers=headers, data=data) as response:
                    if response.status in [200, 201]:
                        result = await response.json()
                        if result.get('code') == 200 and 'data' in result:
                            task_id = result['data']['taskId']
                            return await self.wait_for_fashion_task(task_id, player_name, player_id)
                        else:
                            print(f"❌ [{player_name}] 换装任务创建失败: {result}")
                            return None
                    else:
                        print(f"❌ [{player_name}] 换装API请求失败: {response.status}")
                        return None
                        
        except Exception as e:
            print(f"❌ [{player_name}] 换装请求失败: {e}")
            return None
    
    async def wait_for_fashion_task(self, task_id, player_name, player_id):
        """等待换装任务完成"""
        url = f"{PHOTO_PROCESS_CONFIG['base_url']}/302/comfyui/clothes-changer/check-task-status"
        headers = {"Authorization": f"Bearer {PHOTO_PROCESS_CONFIG['api_key']}"}
        
        max_attempts = PHOTO_PROCESS_CONFIG["timeout"]["max_retry_attempts"]
        attempt = 0
        
        while attempt < max_attempts:
            attempt += 1
            
            try:
                params = {"taskId": task_id}
                async with self.session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        status = result.get('data', 'UNKNOWN')
                        
                        if status == 'SUCCESS' and 'output' in result:
                            output = result['output']
                            result_url = output.get('resultUrl', '')
                            
                            # 下载结果图片
                            output_path = os.path.join(self.team_output_dir, "fashion", f"{player_id}_{player_name}_fashion.png")
                            return await self.download_image(result_url, output_path)
                            
                        elif status in ['RUNNING', 'QUEUED', 'SUBMITTING']:
                            await asyncio.sleep(PHOTO_PROCESS_CONFIG["timeout"]["task_check_interval"])
                            
                        else:
                            print(f"❌ [{player_name}] 换装任务失败，状态: {status}")
                            return None
                    else:
                        await asyncio.sleep(PHOTO_PROCESS_CONFIG["timeout"]["task_check_interval"])
                        continue
                        
            except Exception as e:
                print(f"❌ [{player_name}] 查询换装任务失败: {e}")
                await asyncio.sleep(PHOTO_PROCESS_CONFIG["timeout"]["task_check_interval"])
                continue
        
        print(f"⏰ [{player_name}] 换装任务等待超时")
        return None
    
    async def remove_background(self, image_path, player_name, player_id):
        """背景去除处理"""
        print(f"🎯 [{player_name}] 开始背景去除")
        
        if not os.path.exists(image_path):
            print(f"❌ [{player_name}] 图片文件不存在: {image_path}")
            return None
        
        url = f"{PHOTO_PROCESS_CONFIG['base_url']}/clipdrop/remove-background/v1"
        headers = {"x-api-key": PHOTO_PROCESS_CONFIG['api_key']}
        
        try:
            async with aiofiles.open(image_path, 'rb') as image_file:
                image_content = await image_file.read()
                
                data = aiohttp.FormData()
                data.add_field('image_file', image_content,
                              filename=os.path.basename(image_path),
                              content_type='image/png')
                
                async with self.session.post(url, headers=headers, data=data) as response:
                    if response.status == 200:
                        output_path = os.path.join(self.team_output_dir, "no_background", f"{player_id}_{player_name}_no_bg.png")
                        
                        content = await response.read()
                        async with aiofiles.open(output_path, 'wb') as f:
                            await f.write(content)
                        
                        print(f"✅ [{player_name}] 背景去除完成")
                        return output_path
                    else:
                        print(f"❌ [{player_name}] 背景去除失败: {response.status}")
                        return None
                        
        except Exception as e:
            print(f"❌ [{player_name}] 背景去除请求失败: {e}")
            return None
    
    async def add_white_background(self, subject_image_path, player_name, player_id):
        """添加白底背景"""
        print(f"🎯 [{player_name}] 开始添加白底")
        
        if not os.path.exists(subject_image_path):
            print(f"❌ [{player_name}] 图片文件不存在: {subject_image_path}")
            return None
        
        # 使用线程池执行PIL操作
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            try:
                result = await loop.run_in_executor(
                    executor,
                    self._process_white_background_sync,
                    subject_image_path,
                    player_name,
                    player_id
                )
                return result
            except Exception as e:
                print(f"❌ [{player_name}] 白底处理错误: {e}")
                return None
    
    def _process_white_background_sync(self, subject_image_path, player_name, player_id):
        """同步处理白底背景"""
        with pil_lock:
            try:
                # 打开主体图片
                subject = Image.open(subject_image_path).convert("RGBA")
                width, height = subject.size
                
                # 创建白色背景
                background = Image.new('RGB', (width, height), 
                                     PHOTO_PROCESS_CONFIG["white_background"]["background_color"])
                
                # 合成图片
                background.paste(subject, (0, 0), subject)
                
                # 保存结果
                output_path = os.path.join(self.team_output_dir, "white_background", f"{player_id}_{player_name}_white_bg.png")
                background.save(output_path, PHOTO_PROCESS_CONFIG["white_background"]["output_format"],
                               dpi=(PHOTO_PROCESS_CONFIG["white_background"]["dpi"], 
                                   PHOTO_PROCESS_CONFIG["white_background"]["dpi"]))
                
                print(f"✅ [{player_name}] 白底添加完成")
                return output_path
                
            except Exception as e:
                print(f"❌ [{player_name}] PIL处理错误: {e}")
                return None
    
    async def download_image(self, url, output_path):
        """下载图片"""
        if not url:
            return None
        
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    content = await response.read()
                    async with aiofiles.open(output_path, 'wb') as f:
                        await f.write(content)
                    return output_path
                else:
                    print(f"❌ 下载失败: {response.status}")
                    return None
        except Exception as e:
            print(f"❌ 下载错误: {e}")
            return None

async def process_team_photos(team_name, players, clothes_image_path=None, 
                            process_fashion=False, process_background=False, process_white=False):
    """批量处理球队照片"""
    print(f"🚀 开始处理球队 {team_name} 的照片")
    
    results = []
    
    async with TeamPhotoProcessor(team_name) as processor:
        for player in players:
            if player['photo']:
                photo_path = os.path.join("uploads", team_name, player['photo'])
                if os.path.exists(photo_path):
                    result = await processor.process_player_photo(
                        player, photo_path, clothes_image_path,
                        process_fashion, process_background, process_white
                    )
                    results.append(result)
                else:
                    print(f"❌ 球员 {player['name']} 的照片不存在: {photo_path}")
    
    return results

if __name__ == "__main__":
    # 测试代码
    print("📸 球队照片处理模块")
    print("此模块集成了AI换装、背景去除、白底添加功能")
