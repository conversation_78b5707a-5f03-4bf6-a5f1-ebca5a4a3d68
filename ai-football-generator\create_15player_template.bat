@echo off
echo 🔧 创建支持15个球员的模板...

REM 复制现有模板
copy template.docx template_15players.docx

echo ✅ 已复制模板文件为 template_15players.docx
echo 📝 请手动用Word打开该文件，添加球员11-15的占位符
echo.
echo 需要添加的标签格式：
echo 球员11: {{player11Number}} {{@player11Photo}} {{player11Name}}
echo 球员12: {{player12Number}} {{@player12Photo}} {{player12Name}}
echo 球员13: {{player13Number}} {{@player13Photo}} {{player13Name}}
echo 球员14: {{player14Number}} {{@player14Photo}} {{player14Name}}
echo 球员15: {{player15Number}} {{@player15Photo}} {{player15Name}}
echo.
echo 📸 还需要添加以下照片文件到photos目录：
echo - player11.jpg/png
echo - player12.jpg/png  
echo - player13.jpg/png
echo - player14.jpg/png
echo - player15.jpg/png
echo.
echo 📋 完成后修改config.properties文件：
echo template.path=template_15players.docx
echo.
pause
