import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 球员数据解析器
 * 从AI的回复中提取结构化的球员和队伍信息
 */
public class PlayerDataParser {
    private ObjectMapper objectMapper;
    
    public PlayerDataParser() {
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 解析AI回复中的JSON数据
     */
    public FootballTeamData parseFromAIResponse(String aiResponse) {
        try {
            // 首先尝试提取JSON部分
            String jsonString = extractJsonFromResponse(aiResponse);
            if (jsonString == null) {
                return null;
            }
            
            // 解析JSON
            JsonNode rootNode = objectMapper.readTree(jsonString);
            FootballTeamData teamData = new FootballTeamData();
            
            // 解析队伍信息
            if (rootNode.has("teamInfo")) {
                TeamInfo teamInfo = parseTeamInfo(rootNode.get("teamInfo"));
                teamData.setTeamInfo(teamInfo);
            }
            
            // 解析球员信息
            if (rootNode.has("players")) {
                PlayerData[] players = parsePlayers(rootNode.get("players"));
                teamData.setPlayers(players);
            }
            
            return teamData;
            
        } catch (Exception e) {
            System.err.println("❌ 解析AI回复失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 从AI回复中提取JSON字符串
     */
    private String extractJsonFromResponse(String response) {
        // 查找JSON代码块
        Pattern jsonBlockPattern = Pattern.compile("```json\\s*([\\s\\S]*?)\\s*```", Pattern.CASE_INSENSITIVE);
        Matcher matcher = jsonBlockPattern.matcher(response);
        
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        
        // 如果没有代码块，尝试查找大括号包围的JSON
        Pattern jsonPattern = Pattern.compile("\\{[\\s\\S]*\\}");
        matcher = jsonPattern.matcher(response);
        
        if (matcher.find()) {
            return matcher.group().trim();
        }
        
        return null;
    }
    
    /**
     * 解析队伍基本信息
     */
    private TeamInfo parseTeamInfo(JsonNode teamInfoNode) {
        TeamInfo teamInfo = new TeamInfo();
        
        if (teamInfoNode.has("title")) {
            teamInfo.setTitle(teamInfoNode.get("title").asText());
        }
        if (teamInfoNode.has("organizationName")) {
            teamInfo.setOrganizationName(teamInfoNode.get("organizationName").asText());
        }
        if (teamInfoNode.has("teamLeader")) {
            teamInfo.setTeamLeader(teamInfoNode.get("teamLeader").asText());
        }
        if (teamInfoNode.has("coach")) {
            teamInfo.setCoach(teamInfoNode.get("coach").asText());
        }
        if (teamInfoNode.has("teamDoctor")) {
            teamInfo.setTeamDoctor(teamInfoNode.get("teamDoctor").asText());
        }
        
        return teamInfo;
    }
    
    /**
     * 解析球员信息数组
     */
    private PlayerData[] parsePlayers(JsonNode playersNode) {
        PlayerData[] players = new PlayerData[10]; // 最多10个球员
        
        if (playersNode.isArray()) {
            int index = 0;
            for (JsonNode playerNode : playersNode) {
                if (index >= 10) break; // 最多10个球员
                
                PlayerData player = new PlayerData();
                
                if (playerNode.has("number")) {
                    player.setNumber(playerNode.get("number").asText());
                }
                if (playerNode.has("name")) {
                    player.setName(playerNode.get("name").asText());
                }
                if (playerNode.has("photoPath")) {
                    String photoPath = playerNode.get("photoPath").asText();
                    // 确保路径包含photos/前缀
                    if (!photoPath.startsWith("photos/")) {
                        photoPath = "photos/" + photoPath;
                    }
                    player.setPhotoPath(photoPath);
                } else {
                    // 如果没有指定照片路径，生成默认路径
                    player.setPhotoPath("photos/player" + (index + 1) + ".jpg");
                }
                
                players[index] = player;
                index++;
            }
        }
        
        return players;
    }
    
    /**
     * 检查AI回复是否包含完整的数据
     */
    public boolean isCompleteData(String aiResponse) {
        String jsonString = extractJsonFromResponse(aiResponse);
        if (jsonString == null) {
            return false;
        }
        
        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);
            
            // 检查是否包含队伍信息和球员信息
            boolean hasTeamInfo = rootNode.has("teamInfo");
            boolean hasPlayers = rootNode.has("players") && rootNode.get("players").isArray();
            
            return hasTeamInfo && hasPlayers;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 验证解析后的数据是否完整
     */
    public boolean validateTeamData(FootballTeamData teamData) {
        if (teamData == null) {
            return false;
        }
        
        // 检查队伍信息
        TeamInfo teamInfo = teamData.getTeamInfo();
        if (teamInfo == null || 
            isEmptyString(teamInfo.getTitle()) ||
            isEmptyString(teamInfo.getOrganizationName())) {
            return false;
        }
        
        // 检查球员信息（至少要有1个球员）
        int playerCount = teamData.getPlayerCount();
        if (playerCount == 0) {
            return false;
        }
        
        // 检查每个球员的信息是否完整
        for (int i = 0; i < teamData.getPlayers().length; i++) {
            PlayerData player = teamData.getPlayer(i);
            if (player != null) {
                if (isEmptyString(player.getName()) || isEmptyString(player.getNumber())) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    private boolean isEmptyString(String str) {
        return str == null || str.trim().isEmpty();
    }
}
