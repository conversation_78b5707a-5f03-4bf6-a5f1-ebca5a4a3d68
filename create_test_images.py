from PIL import Image, ImageDraw, ImageFont
import os

def create_test_image(filename, text, color):
    """创建测试图片"""
    # 创建图片
    img = Image.new('RGB', (100, 120), color=color)
    draw = ImageDraw.Draw(img)
    
    # 尝试使用默认字体
    try:
        font = ImageFont.truetype("arial.ttf", 12)
    except:
        font = ImageFont.load_default()
    
    # 添加文字
    text_bbox = draw.textbbox((0, 0), text, font=font)
    text_width = text_bbox[2] - text_bbox[0]
    text_height = text_bbox[3] - text_bbox[1]
    
    x = (100 - text_width) // 2
    y = (120 - text_height) // 2
    
    draw.text((x, y), text, fill='white', font=font)
    
    # 保存图片
    img.save(filename, 'PNG')
    print(f"创建测试图片: {filename}")

# 创建三个测试图片
create_test_image('player1.png', 'Player 1', 'blue')
create_test_image('player2.png', 'Player 2', 'green') 
create_test_image('player3.png', 'Player 3', 'red')

print("所有测试图片创建完成！")
