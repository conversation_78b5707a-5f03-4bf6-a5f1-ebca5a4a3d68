# 📸 Batch Results 照片处理说明

## 🎯 功能说明

这个工具可以将 `batch_results` 文件夹中的球员照片自动应用到 `template_15players.docx` Word模板中，生成完整的足球报名表。

## 📁 文件结构

```
ai-football-generator/
├── batch_results/                    # 输入照片目录
│   ├── player01_final.png           # 球员1的最终照片
│   ├── player02_final.png           # 球员2的最终照片
│   ├── ...                          # 更多球员照片
│   └── player13_final.png           # 球员13的最终照片
├── template_15players.docx          # Word模板文件
├── output/                          # 输出目录（自动创建）
└── process_batch_results.bat        # 处理脚本
```

## 🚀 使用方法

### 方法1：双击运行（推荐）
1. 确保 `batch_results` 文件夹中有球员照片
2. 双击运行 `process_batch_results.bat`
3. 等待处理完成
4. 查看 `output` 目录中生成的Word文档

### 方法2：命令行运行
```bash
# 进入项目目录
cd ai-football-generator

# 运行处理脚本
process_batch_results.bat
```

## 📋 处理流程

1. **环境检查**：检查Java和Maven环境
2. **文件检查**：验证必要的文件和目录存在
3. **项目编译**：编译Java代码
4. **照片处理**：读取batch_results中的照片
5. **模板填充**：将照片插入到Word模板中
6. **文档生成**：生成最终的Word报名表

## 📸 支持的照片格式

- **文件名格式**：`player01_final.png` 到 `player15_final.png`
- **图片格式**：PNG、JPG、JPEG
- **自动处理**：程序会自动调整图片大小为 100x120 像素

## 📄 生成的文档内容

生成的Word文档包含：
- **基本信息**：队伍标题、单位名称、领队、教练、队医
- **球员信息**：最多15个球员的号码、姓名和照片
- **自动布局**：专业的表格布局和格式

## ⚠️ 注意事项

1. **照片数量**：支持1-15个球员照片
2. **文件命名**：照片文件名必须严格按照 `playerXX_final.png` 格式
3. **模板文件**：确保 `template_15players.docx` 存在
4. **输出目录**：生成的文件会保存在 `output` 目录中

## 🔧 环境要求

- **Java**：Java 8 或更高版本
- **Maven**：Maven 3.6 或更高版本
- **操作系统**：Windows（支持批处理脚本）

## 📊 示例输出

```
🚀 批量处理batch_results中的照片到Word模板
================================================
✅ 环境检查通过
🔧 正在编译项目...
✅ 编译成功
🚀 开始处理batch_results中的照片...
✅ 添加球员1照片: batch_results/player01_final.png
✅ 添加球员2照片: batch_results/player02_final.png
...
🎉 Word文档生成成功！
📄 文件位置: output/batch_results_report_1755753123456.docx
```

## ❓ 常见问题

### Q: 照片没有显示在Word文档中？
A: 检查以下几点：
- 照片文件是否存在于 `batch_results` 目录
- 文件名是否符合 `playerXX_final.png` 格式
- 图片文件是否损坏

### Q: 编译失败怎么办？
A: 确保：
- Java和Maven环境正确安装
- 网络连接正常（Maven需要下载依赖）
- 项目目录完整

### Q: 如何修改球员信息？
A: 目前程序使用默认的球员信息（球员1、球员2等）。如需自定义，可以修改 `BatchResultsProcessor.java` 中的球员姓名设置。

## 🔄 自定义修改

如需修改球员姓名或其他信息，编辑 `src/main/java/BatchResultsProcessor.java` 文件中的以下部分：

```java
// 设置球员号码和姓名
data.put("player" + i + "Number", String.valueOf(i));
data.put("player" + i + "Name", "球员" + i);  // 在这里修改球员姓名
```

修改后重新运行 `process_batch_results.bat` 即可。
