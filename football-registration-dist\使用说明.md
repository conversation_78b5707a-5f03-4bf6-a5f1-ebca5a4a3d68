# 足球报名表生成器使用说明

## 📁 项目结构

```
football-registration/
├── src/
│   └── main/
│       └── java/
│           └── FootballRegistrationApp.java
├── photos/                          # 球员照片目录
│   ├── player0.jpg
│   ├── player1.jpg
│   ├── player3.jpg
│   ├── player5.jpg
│   ├── player6.jpg
│   ├── player7.jpg
│   ├── player8.jpg
│   ├── player9.jpg
│   ├── player10.jpg
│   └── player11.jpg
├── football_template.docx           # Word模板文件
├── pom.xml                         # Maven配置
└── README.md
```

## 🚀 快速开始

### 步骤1：修改您的Word文档

1. **打开您现有的Word文档**
2. **修改以下位置**：

**标题处：**
```
附件1 {{title}}
```

**基本信息表格中：**
- 单位名称处改为：`{{organizationName}}`
- 领队姓名处改为：`{{teamLeader}}`
- 教练姓名处改为：`{{coach}}`
- 球衣颜色处改为：`{{jerseyColor}}`

**球员表格区域：**
- 将整个球员信息表格删除
- 在该位置输入：`{{#playerTable}}`

3. **保存为：** `football_template.docx`

### 步骤2：准备球员照片

在项目根目录创建 `photos` 文件夹，放入球员照片：
- `player0.jpg` - 0号球员照片
- `player1.jpg` - 1号球员照片
- ... 以此类推

### 步骤3：编译运行

```bash
# 编译项目
mvn clean compile

# 运行程序
mvn exec:java -Dexec.mainClass="FootballRegistrationApp"

# 或者打包后运行
mvn clean package
java -jar target/football-registration-1.0.0.jar
```

## 🔧 自定义配置

### 修改基本信息

在 `FootballRegistrationApp.java` 中修改：

```java
private static Map<String, Object> prepareFormData() {
    Map<String, Object> data = new HashMap<>();
    
    // 修改这些信息
    data.put("title", "您的比赛名称");
    data.put("organizationName", "您的单位名称");
    data.put("teamLeader", "您的领队姓名");
    data.put("coach", "您的教练姓名");
    data.put("jerseyColor", "您的球衣颜色");
    
    return data;
}
```

### 修改球员信息

在 `createPlayerRow` 方法中修改球员号码和姓名：

```java
// 第一行球员
rows.add(createPlayerRow("10", "张雷", "0", "白浩", "3", "翟召昌", "11", "赵飞", "5", "王洪艺"));

// 第二行球员  
rows.add(createPlayerRow("6", "霍乃峰", "7", "马香海", "8", "张继姿", "9", "王强", "1", "彭浩"));
```

## 📸 照片要求

- **格式**：JPG、PNG
- **尺寸**：建议 300x400 像素
- **命名**：`player{号码}.jpg`，如 `player10.jpg`
- **位置**：放在 `photos/` 目录下

## 🎯 输出结果

运行成功后会生成：
`淄川区2025年五人制足球比赛报名表_完整版.docx`

## ⚠️ 常见问题

### 1. 模板文件找不到
确保 `football_template.docx` 在项目根目录

### 2. 照片显示不了
- 检查照片文件是否存在
- 检查文件名是否正确
- 确保照片格式为 JPG/PNG

### 3. 中文乱码
确保：
- 项目编码为 UTF-8
- Word模板保存时选择正确编码

### 4. 表格格式不对
检查模板中的 `{{#playerTable}}` 标记是否正确

## 🔄 批量生成

如需为多个队伍生成报名表：

```java
public static void generateMultipleTeams() {
    String[] teams = {"太河镇", "昆仑镇", "寨里镇"};
    
    for (String team : teams) {
        Map<String, Object> data = prepareFormData();
        data.put("organizationName", team + "人民政府");
        
        XWPFTemplate.compile("football_template.docx")
            .render(data)
            .writeToFile(team + "_报名表.docx");
    }
}
```

## 📞 技术支持

如遇问题，请检查：
1. Java版本（需要Java 8+）
2. Maven配置
3. poi-tl依赖版本
4. 模板文件格式

## 🎉 完成！

按照以上步骤，您就可以自动生成专业的足球报名表了！
