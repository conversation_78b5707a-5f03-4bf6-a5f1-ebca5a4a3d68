#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出服务
Export Service

提供数据导出相关的业务逻辑
"""

import os
import json
from datetime import datetime
from typing import Dict, Any, Optional
import streamlit as st

from models.team import Team
from data.team_repository import TeamRepository
from data.file_manager import FileManager
from config.settings import app_settings
from utils.helpers import get_safe_team_name


class ExportService:
    """导出服务"""

    def __init__(self, user_id: str = None):
        self.team_repo = TeamRepository(user_id)
        self.file_manager = FileManager(user_id)
        if user_id:
            self.ai_export_folder = os.path.join(app_settings.paths.AI_EXPORT_FOLDER, user_id)
        else:
            self.ai_export_folder = app_settings.paths.AI_EXPORT_FOLDER
    
    def auto_export_for_ai(self, team_name: str) -> Optional[str]:
        """
        自动导出数据到AI处理文件夹
        
        Args:
            team_name: 球队名称
            
        Returns:
            Optional[str]: 导出文件路径，如果失败返回None
        """
        team = self.team_repo.load_team(team_name)
        if not team or not team.players:
            return None  # 没有球员数据，不需要导出
        
        # 为AI处理准备数据格式
        export_data = {
            'team_info': {
                'name': team_name,
                'display_name': '默认球队' if team_name == 'default' else team_name,
                'created_at': team.team_info.created_at,
                'total_players': team.total_players
            },
            'players': [],
            'export_time': datetime.now().isoformat(),
            'ai_processing': {
                'status': 'ready',
                'next_steps': ['photo_processing', 'form_generation'],
                'photo_folder': f"uploads/{get_safe_team_name(team_name)}",
                'export_folder': self.ai_export_folder
            }
        }
        
        # 处理球员数据
        for player in team.players:
            # 获取照片的路径信息
            photo_abs_path = None
            photo_relative_path = None
            photo_exists = False
            
            if player.photo:
                photo_relative_path = f"uploads/{get_safe_team_name(team_name)}/{player.photo}"
                photo_abs_path = os.path.abspath(photo_relative_path)
                photo_exists = os.path.exists(photo_abs_path)
            
            player_data = {
                'id': player.id,
                'name': player.name,
                'jersey_number': player.jersey_number,
                'photo_info': {
                    'filename': player.photo,
                    'relative_path': photo_relative_path,
                    'absolute_path': photo_abs_path,
                    'exists': photo_exists
                },
                'created_at': player.created_at,
                'ai_tags': {
                    'person_id': f"{team_name}_{player.jersey_number}_{player.name}",
                    'display_name': player.display_name,
                    'team': team_name
                }
            }
            export_data['players'].append(player_data)
        
        # 保存到AI处理文件夹
        safe_team_name = get_safe_team_name(team_name)
        ai_export_file = os.path.join(
            self.ai_export_folder, 
            f'team_{safe_team_name}_ai_ready.json'
        )
        
        try:
            # 确保导出目录存在
            os.makedirs(self.ai_export_folder, exist_ok=True)
            
            with open(ai_export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            return ai_export_file
        except Exception as e:
            st.error(f"自动导出失败: {e}")
            return None
    
    def export_team_data(self, team_name: str) -> Dict[str, Any]:
        """
        导出球队数据（用于手动下载）
        
        Args:
            team_name: 球队名称
            
        Returns:
            Dict[str, Any]: 导出的数据
        """
        team = self.team_repo.load_team(team_name)
        if not team:
            return {}
        
        # 为下载准备数据格式
        export_data = {
            'team_info': team.team_info.to_dict(),
            'players': [],
            'export_time': datetime.now().isoformat(),
            'total_players': team.total_players
        }
        
        for player in team.players:
            player_data = {
                'id': player.id,
                'name': player.name,
                'jersey_number': player.jersey_number,
                'photo_path': f"uploads/{get_safe_team_name(team_name)}/{player.photo}" if player.photo else None,
                'photo_file': player.photo,
                'created_at': player.created_at,
                'updated_at': player.updated_at
            }
            export_data['players'].append(player_data)
        
        return export_data
    
    def export_to_json_string(self, team_name: str) -> str:
        """
        导出为JSON字符串
        
        Args:
            team_name: 球队名称
            
        Returns:
            str: JSON字符串
        """
        export_data = self.export_team_data(team_name)
        return json.dumps(export_data, ensure_ascii=False, indent=2)
    
    def get_export_filename(self, team_name: str) -> str:
        """
        获取导出文件名
        
        Args:
            team_name: 球队名称
            
        Returns:
            str: 文件名
        """
        safe_name = get_safe_team_name(team_name)
        date_str = datetime.now().strftime('%Y%m%d')
        return f"team_{safe_name}_{date_str}.json"
    
    def export_processing_config(self, config_data: Dict[str, Any]) -> str:
        """
        导出处理配置
        
        Args:
            config_data: 配置数据
            
        Returns:
            str: JSON字符串
        """
        return json.dumps(config_data, ensure_ascii=False, indent=2)
    
    def get_ai_export_files(self) -> list:
        """
        获取AI导出文件列表
        
        Returns:
            list: 文件列表
        """
        if not os.path.exists(self.ai_export_folder):
            return []
        
        files = []
        for filename in os.listdir(self.ai_export_folder):
            if filename.endswith('_ai_ready.json'):
                file_path = os.path.join(self.ai_export_folder, filename)
                file_info = {
                    'filename': filename,
                    'path': file_path,
                    'size': os.path.getsize(file_path),
                    'modified_time': datetime.fromtimestamp(
                        os.path.getmtime(file_path)
                    ).isoformat()
                }
                files.append(file_info)
        
        return files
    
    def load_ai_export_file(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        加载AI导出文件
        
        Args:
            filename: 文件名
            
        Returns:
            Optional[Dict[str, Any]]: 文件内容，如果失败返回None
        """
        file_path = os.path.join(self.ai_export_folder, filename)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            st.error(f"加载AI导出文件失败: {e}")
            return None
