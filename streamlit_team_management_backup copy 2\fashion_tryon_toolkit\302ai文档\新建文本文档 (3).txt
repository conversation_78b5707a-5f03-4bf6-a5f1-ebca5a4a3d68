# 查询换装任务状态

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /302/comfyui/clothes-changer/check-task-status:
    get:
      summary: 查询换装任务状态
      deprecated: false
      description: |-
        通过comfyUI复杂工作流实现的换装效果，商用级效果，适用于模特换装，运行时长3-5分钟

        **价格：免费**
      tags:
        - 图片处理/302.AI-ComfyUI
      parameters:
        - name: taskId
          in: query
          description: ''
          required: true
          example: 任务创建后获取的taskId
          schema:
            type: string
        - name: Authorization
          in: header
          description: ''
          required: true
          example: Bearer {{YOUR_API_KEY}}
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  data:
                    type: string
                    title: 状态
                    description: 任务的状态：SUCCESS、RUNNING、QUEUED
                  queue:
                    type: integer
                  output:
                    type: object
                    properties:
                      comparisonUrl:
                        type: string
                        title: 比对图url
                      resultUrl:
                        type: string
                        title: 结果图url
                    required:
                      - comparisonUrl
                      - resultUrl
                    title: 任务成功后返回的结果
                    x-apifox-orders:
                      - comparisonUrl
                      - resultUrl
                required:
                  - code
                  - data
                  - queue
                  - output
                x-apifox-orders:
                  - code
                  - data
                  - queue
                  - output
              example:
                code: 0
                data: SUCCESS
                queue: 0
                output:
                  comparisonUrl: >-
                    https://file.302.ai/gpt/imgs/20250311/0bbdc1b202da4e56a928c7dc6b7a46e0.png
                  resultUrl: >-
                    https://file.302.ai/gpt/imgs/20250311/451d45a81f064a56bc2c73155047c7f8.png
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 图片处理/302.AI-ComfyUI
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/4012774/apis/api-270156337-run
components:
  schemas: {}
  securitySchemes: {}
servers:
  - url: https://api.302.ai
    description: 正式环境
  - url: https://api.302ai.cn
    description: 国内中转
security: []

```


import http.client

conn = http.client.HTTPSConnection("api.302.ai")
payload = ''
headers = {
   'Authorization': 'Bearer '
}
conn.request("GET", "/302/comfyui/clothes-changer/check-task-status?taskId=%E4%BB%BB%E5%8A%A1%E5%88%9B%E5%BB%BA%E5%90%8E%E8%8E%B7%E5%8F%96%E7%9A%84taskId", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))

import requests

url = "https://api.302.ai/302/comfyui/clothes-changer/check-task-status?taskId=任务创建后获取的taskId"

payload={}
headers = {
   'Authorization': 'Bearer '
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)