/*
 * Copyright 2014-2025 Sayi
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.deepoove.poi.policy;

import com.deepoove.poi.converter.ToRenderDataConverter;
import com.deepoove.poi.data.ParagraphRenderData;
import com.deepoove.poi.render.RenderContext;

/**
 * Use paragraph renderer policy render type <T>
 * 
 * <AUTHOR>
 *
 * @param <T>
 */
public abstract class AbstractParagraphConverterRenderPolicy<T> extends AbstractRenderPolicy<T> {

    protected final ToRenderDataConverter<T, ParagraphRenderData> paragraphConverter;

    public abstract ToRenderDataConverter<T, ParagraphRenderData> getParagraphRenderDataConverter();

    public AbstractParagraphConverterRenderPolicy() {
        this.paragraphConverter = getParagraphRenderDataConverter();
    }

    @Override
    protected void afterRender(RenderContext<T> context) {
        super.clearPlaceholder(context, true);
    }

    @Override
    public void doRender(RenderContext<T> context) throws Exception {
        ParagraphRenderPolicy.Helper.renderParagraph(context.getRun(), paragraphConverter.convert(context.getData()));
    }

}
