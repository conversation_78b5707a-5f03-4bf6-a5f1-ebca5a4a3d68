#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI智能信息提取页面
使用OpenAI结构化输出和函数调用功能
"""

import streamlit as st
import sys
import os
import json
from datetime import datetime
from typing import Dict, Any

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from services.ai_service import AIService
from services.team_service import TeamService
from services.player_service import PlayerService
from services.auth_service import AuthService
from config.settings import app_settings


def configure_page():
    """配置页面设置"""
    st.set_page_config(
        page_title="AI智能信息提取",
        page_icon="🤖",
        layout="wide",
        initial_sidebar_state="expanded"
    )


def show_ai_extraction_page():
    """显示AI智能信息提取页面"""
    st.title("🤖 AI智能信息提取")
    st.markdown("---")
    
    # 初始化服务
    auth_service = AuthService()
    user_id = auth_service.get_current_user_id()

    # 检查用户是否已登录
    if not user_id:
        st.error("❌ 请先登录系统")
        st.info("💡 请返回主页面进行登录")
        return

    ai_service = AIService(user_id)
    team_service = TeamService()
    player_service = PlayerService()
    
    # 检查AI服务可用性
    if not ai_service.is_available():
        st.error("❌ AI服务不可用，请检查配置")
        return
    
    # 显示功能状态
    col1, col2 = st.columns(2)
    with col1:
        if ai_service.has_enhanced_features():
            st.success("✅ 增强AI功能已启用")
        else:
            st.warning("⚠️ 仅基础AI功能可用")
    
    with col2:
        st.info(f"👤 用户ID: {user_id[:8]}...")
    
    # 选择提取类型
    st.markdown("### 📋 选择信息提取类型")
    extraction_type = st.selectbox(
        "请选择要提取的信息类型",
        ["球队信息", "球员信息", "比赛信息"],
        help="选择您要从文本中提取的信息类型"
    )
    
    # 文本输入区域
    st.markdown("### 📝 输入文本信息")
    
    if extraction_type == "球队信息":
        placeholder_text = """请输入球队相关信息，例如：
我们是蓝鹰足球俱乐部，联系人是张三，电话13800138000。
教练是李四，电话13900139000。
我们要参加2024年淄川市五人制足球联赛，比赛时间是9月15日，地点在淄川体育中心。
参赛组别是成年男子组。"""
    elif extraction_type == "球员信息":
        placeholder_text = """请输入球员信息，例如：
1. 张三，10号，前锋，电话13800138000
2. 李四，9号，中场，电话13900139000
3. 王五，1号，守门员，电话13700137000"""
    else:
        placeholder_text = """请输入比赛相关信息，例如：
比赛名称：2024年淄川市五人制足球联赛
比赛时间：2024年9月15日
比赛地点：淄川体育中心
参赛组别：成年男子组"""
    
    input_text = st.text_area(
        "请输入要提取信息的文本",
        placeholder=placeholder_text,
        height=200,
        help="支持自然语言输入，AI会智能识别和提取结构化信息"
    )
    
    # 提取选项
    st.markdown("### ⚙️ 提取选项")
    col1, col2 = st.columns(2)
    
    with col1:
        use_structured_output = st.checkbox(
            "使用结构化输出",
            value=True,
            help="使用OpenAI的结构化输出功能确保数据格式正确"
        )
    
    with col2:
        use_function_calling = st.checkbox(
            "使用函数调用",
            value=True,
            help="使用OpenAI的函数调用功能进行智能数据处理"
        )
    
    # 提取按钮
    if st.button("🚀 开始提取", type="primary", use_container_width=True):
        if not input_text.strip():
            st.error("请输入要提取的文本内容")
            return
        
        with st.spinner("🤖 AI正在分析和提取信息..."):
            try:
                # 根据类型选择提取方法
                if extraction_type == "球队信息":
                    result = extract_team_information(
                        ai_service, input_text, use_structured_output, use_function_calling
                    )
                elif extraction_type == "球员信息":
                    result = extract_player_information(
                        ai_service, input_text, use_structured_output, use_function_calling
                    )
                else:
                    result = extract_competition_information(
                        ai_service, input_text, use_structured_output, use_function_calling
                    )
                
                # 显示结果
                display_extraction_results(result, extraction_type)
                
            except Exception as e:
                st.error(f"❌ 信息提取失败: {e}")


def extract_team_information(
    ai_service: AIService, 
    text: str, 
    use_structured: bool, 
    use_functions: bool
) -> Dict[str, Any]:
    """提取球队信息"""
    if ai_service.has_enhanced_features() and use_functions:
        return ai_service.extract_team_info_from_text(text)
    else:
        # 使用基础AI功能
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的信息提取助手。请从用户提供的文本中提取球队信息，包括球队名称、联系人、电话、教练信息、比赛信息等。请以JSON格式返回结构化数据。"
            },
            {
                "role": "user",
                "content": f"请从以下文本中提取球队信息：\n\n{text}"
            }
        ]
        
        if use_structured:
            response = ai_service.generate_enhanced_response(messages, use_functions, use_structured)
        else:
            response = ai_service.generate_response(messages)
        
        return {"response": response, "extracted_data": None}


def extract_player_information(
    ai_service: AIService, 
    text: str, 
    use_structured: bool, 
    use_functions: bool
) -> Dict[str, Any]:
    """提取球员信息"""
    if ai_service.has_enhanced_features() and use_functions:
        return ai_service.extract_player_info_from_text(text)
    else:
        # 使用基础AI功能
        messages = [
            {
                "role": "system",
                "content": "你是一个专业的信息提取助手。请从用户提供的文本中提取球员信息，包括姓名、号码、位置、联系方式等。请以JSON格式返回结构化数据。"
            },
            {
                "role": "user",
                "content": f"请从以下文本中提取球员信息：\n\n{text}"
            }
        ]
        
        if use_structured:
            response = ai_service.generate_enhanced_response(messages, use_functions, use_structured)
        else:
            response = ai_service.generate_response(messages)
        
        return {"response": response, "extracted_data": None}


def extract_competition_information(
    ai_service: AIService, 
    text: str, 
    use_structured: bool, 
    use_functions: bool
) -> Dict[str, Any]:
    """提取比赛信息"""
    messages = [
        {
            "role": "system",
            "content": "你是一个专业的信息提取助手。请从用户提供的文本中提取比赛信息，包括比赛名称、时间、地点、组别等。请以JSON格式返回结构化数据。"
        },
        {
            "role": "user",
            "content": f"请从以下文本中提取比赛信息：\n\n{text}"
        }
    ]
    
    if use_structured:
        response = ai_service.generate_enhanced_response(messages, use_functions, use_structured)
    else:
        response = ai_service.generate_response(messages)
    
    return {"response": response, "extracted_data": None}


def display_extraction_results(result: Dict[str, Any], extraction_type: str):
    """显示提取结果"""
    st.markdown("### 📊 提取结果")
    
    # 显示AI回复
    if result.get("response"):
        st.markdown("#### 🤖 AI分析结果")
        st.markdown(result["response"])
    
    # 显示结构化数据
    if result.get("extracted_data"):
        st.markdown("#### 📋 结构化数据")
        
        extracted_data = result["extracted_data"]
        
        if extracted_data.get("success"):
            # 显示提取的信息
            if extraction_type == "球队信息" and extracted_data.get("extracted_info"):
                display_team_data(extracted_data["extracted_info"])
            elif extraction_type == "球员信息" and extracted_data.get("players"):
                display_player_data(extracted_data["players"])
            
            # 显示置信度
            if "confidence" in extracted_data:
                confidence = extracted_data["confidence"]
                st.metric("置信度", f"{confidence:.2%}")
                
                if confidence < 0.7:
                    st.warning("⚠️ 置信度较低，建议检查提取结果")
        else:
            st.error(f"❌ 提取失败: {extracted_data.get('error', '未知错误')}")
    
    # 显示函数调用结果
    if 'last_function_results' in st.session_state:
        st.markdown("#### ⚙️ 函数调用结果")
        with st.expander("查看详细结果"):
            st.json(st.session_state['last_function_results'])


def display_team_data(team_data: Dict[str, Any]):
    """显示球队数据"""
    if "basic_info" in team_data:
        st.markdown("**基本信息:**")
        basic = team_data["basic_info"]
        col1, col2 = st.columns(2)
        with col1:
            if basic.get("team_name"):
                st.write(f"• 球队名称: {basic['team_name']}")
            if basic.get("contact_person"):
                st.write(f"• 联系人: {basic['contact_person']}")
        with col2:
            if basic.get("contact_phone"):
                st.write(f"• 联系电话: {basic['contact_phone']}")
            if basic.get("region"):
                st.write(f"• 所属地区: {basic['region']}")
    
    if "management" in team_data:
        st.markdown("**管理信息:**")
        mgmt = team_data["management"]
        col1, col2 = st.columns(2)
        with col1:
            if mgmt.get("coach_name"):
                st.write(f"• 教练: {mgmt['coach_name']}")
        with col2:
            if mgmt.get("coach_phone"):
                st.write(f"• 教练电话: {mgmt['coach_phone']}")
    
    if "competition" in team_data:
        st.markdown("**比赛信息:**")
        comp = team_data["competition"]
        for key, value in comp.items():
            if value:
                st.write(f"• {key}: {value}")


def display_player_data(players: list):
    """显示球员数据"""
    st.write(f"提取到 {len(players)} 名球员信息:")
    
    for i, player in enumerate(players, 1):
        with st.expander(f"球员 {i}: {player.get('basic_info', {}).get('name', '未知')}"):
            basic = player.get("basic_info", {})
            contact = player.get("contact_info", {})
            
            col1, col2 = st.columns(2)
            with col1:
                if basic.get("name"):
                    st.write(f"姓名: {basic['name']}")
                if basic.get("jersey_number"):
                    st.write(f"号码: {basic['jersey_number']}")
            with col2:
                if basic.get("position"):
                    st.write(f"位置: {basic['position']}")
                if contact.get("phone"):
                    st.write(f"电话: {contact['phone']}")


def main():
    """主函数"""
    configure_page()
    show_ai_extraction_page()


if __name__ == "__main__":
    main()
else:
    # 作为Streamlit页面运行时
    show_ai_extraction_page()
