import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.Pictures;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 简单的poi-tl测试程序
 * 测试基本功能是否正常
 */
public class SimpleTest {
    
    public static void main(String[] args) {
        try {
            // 测试创建简单文档
            testBasicTemplate();
            System.out.println("✅ 基本测试成功！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试基本模板功能
     */
    public static void testBasicTemplate() throws IOException {
        // 准备数据
        Map<String, Object> data = new HashMap<>();
        data.put("title", "测试标题");
        data.put("name", "张三");
        
        // 尝试添加图片
        try {
            data.put("photo", Pictures.ofLocal("player1.png").size(100, 120).create());
            System.out.println("✅ 图片加载成功");
        } catch (Exception e) {
            System.out.println("⚠️  图片加载失败: " + e.getMessage());
        }
        
        // 创建一个简单的模板内容
        String templateContent = "标题: {{title}}\n姓名: {{name}}\n";
        
        System.out.println("📄 模板内容:");
        System.out.println(templateContent);
        System.out.println("\n📊 数据:");
        data.forEach((key, value) -> {
            if (value instanceof String) {
                System.out.println(key + " = " + value);
            } else {
                System.out.println(key + " = [图片对象]");
            }
        });
        
        System.out.println("\n✅ poi-tl库加载成功，数据准备完成！");
        System.out.println("💡 下一步：请创建Word模板文件 template.docx");
    }
}
