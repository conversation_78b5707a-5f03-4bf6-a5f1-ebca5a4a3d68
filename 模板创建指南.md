# 足球报名表Word模板创建指南

## 方法一：手动创建（推荐新手）

### 步骤1：打开Word创建基本结构

1. 打开Microsoft Word
2. 输入标题：`{{title}}`
3. 创建基本信息表格：

```
┌─────────────────┬─────────────────┬─────────┐
│ 单位名称(公章)   │ {{orgName}}      │  队徽   │
├─────────────────┼─────────────────┼─────────┤
│ 领队   │ 姓名   │ {{leader}}       │         │
├─────────────────┼─────────────────┼─────────┤
│ 教练   │ 姓名   │ {{coach}}        │         │
├─────────────────┼─────────────────┼─────────┤
│ 球衣   │ 主场   │ {{jerseyColor}}  │         │
└─────────────────┴─────────────────┴─────────┘
```

### 步骤2：添加球员信息区域

在表格下方添加：
```
{{#playerTable}}
```

### 步骤3：保存模板

保存为：`football_registration_template.docx`

## 方法二：使用代码生成模板

运行 `FootballTemplateCreator.java` 自动生成模板文件。

## poi-tl语法说明

| 语法 | 用途 | 示例 |
|------|------|------|
| `{{变量名}}` | 文本替换 | `{{title}}` |
| `{{@图片变量}}` | 插入图片 | `{{@playerPhoto}}` |
| `{{#表格变量}}` | 插入表格 | `{{#playerTable}}` |
| `{{*列表变量}}` | 插入列表 | `{{*teamList}}` |

## 模板变量对应关系

### 基本信息变量
- `{{title}}` → 报名表标题
- `{{orgName}}` → 单位名称  
- `{{leader}}` → 领队姓名
- `{{coach}}` → 教练姓名
- `{{jerseyColor}}` → 球衣颜色

### 表格变量
- `{{#playerTable}}` → 球员信息表格
- `{{#attachmentTable}}` → 附件表格

## 实际使用示例

### Java代码中的数据准备：
```java
Map<String, Object> data = new HashMap<>();
data.put("title", "淄川区2025年五人制足球比赛报名表");
data.put("orgName", "太河镇人民政府");
data.put("leader", "张三");
data.put("coach", "李四");
data.put("jerseyColor", "蓝色");
data.put("playerTable", createPlayerTable());
```

### 渲染模板：
```java
XWPFTemplate.compile("football_registration_template.docx")
    .render(data)
    .writeToFile("output.docx");
```

## 注意事项

1. **变量名要完全匹配**：模板中的`{{orgName}}`必须与Java代码中的`data.put("orgName", ...)`完全一致

2. **表格语法**：`{{#playerTable}}`会被完整的表格替换

3. **图片语法**：`{{@photo}}`用于插入图片

4. **保存格式**：模板必须保存为`.docx`格式

5. **中文支持**：poi-tl完全支持中文，无需特殊处理

## 模板测试

创建模板后，可以用简单数据测试：

```java
Map<String, Object> testData = new HashMap<>();
testData.put("title", "测试标题");
testData.put("orgName", "测试单位");
// ... 其他测试数据

XWPFTemplate.compile("your_template.docx")
    .render(testData)
    .writeToFile("test_output.docx");
```

这样可以验证模板是否正确创建。
