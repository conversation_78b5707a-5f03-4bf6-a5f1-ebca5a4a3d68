import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.util.TableTools;
import org.apache.poi.xwpf.usermodel.*;

import java.io.*;
import java.util.*;

/**
 * 模板扩展器 - 将10个球员的模板扩展为15个球员
 */
public class TemplateExpander {
    
    public static void main(String[] args) {
        try {
            System.out.println("🔧 开始扩展模板以支持15个球员...");
            
            // 创建支持15个球员的新模板
            createExtendedTemplate();
            
            System.out.println("✅ 模板扩展完成！");
            System.out.println("📄 新模板文件：template_15players.docx");
            
        } catch (Exception e) {
            System.err.println("❌ 模板扩展失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建支持15个球员的扩展模板
     */
    public static void createExtendedTemplate() throws Exception {
        // 创建新的Word文档
        XWPFDocument document = new XWPFDocument();
        
        // 1. 添加标题
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText("{{title}}");
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        
        // 2. 添加空行
        document.createParagraph();
        
        // 3. 创建基本信息表格
        createBasicInfoTable(document);
        
        // 4. 添加空行
        document.createParagraph();
        
        // 5. 创建球员信息表格（支持15个球员）
        createPlayerTable(document);
        
        // 6. 保存文档
        try (FileOutputStream out = new FileOutputStream("template_15players.docx")) {
            document.write(out);
        }
        
        document.close();
    }
    
    /**
     * 创建基本信息表格
     */
    private static void createBasicInfoTable(XWPFDocument document) {
        XWPFTable table = document.createTable(4, 3);
        
        // 设置表格样式
        table.setWidth("100%");
        
        // 第一行：单位名称
        XWPFTableRow row1 = table.getRow(0);
        row1.getCell(0).setText("单位名称(公章)");
        row1.getCell(1).setText("{{organizationName}}");
        row1.getCell(2).setText("队徽");
        
        // 第二行：领队
        XWPFTableRow row2 = table.getRow(1);
        row2.getCell(0).setText("领队");
        row2.getCell(1).setText("{{teamLeader}}");
        row2.getCell(2).setText("");
        
        // 第三行：教练
        XWPFTableRow row3 = table.getRow(2);
        row3.getCell(0).setText("教练");
        row3.getCell(1).setText("{{coach}}");
        row3.getCell(2).setText("");
        
        // 第四行：队医
        XWPFTableRow row4 = table.getRow(3);
        row4.getCell(0).setText("队医");
        row4.getCell(1).setText("{{teamDoctor}}");
        row4.getCell(2).setText("");
    }
    
    /**
     * 创建球员信息表格（支持15个球员）
     */
    private static void createPlayerTable(XWPFDocument document) {
        // 创建球员表格：3行5列，每行显示5个球员
        XWPFTable playerTable = document.createTable(3, 5);
        playerTable.setWidth("100%");
        
        // 填充15个球员的占位符
        for (int row = 0; row < 3; row++) {
            XWPFTableRow tableRow = playerTable.getRow(row);
            
            for (int col = 0; col < 5; col++) {
                int playerNum = row * 5 + col + 1;
                XWPFTableCell cell = tableRow.getCell(col);
                
                // 清空单元格
                cell.removeParagraph(0);
                
                // 添加球员号码
                XWPFParagraph numPara = cell.addParagraph();
                numPara.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun numRun = numPara.createRun();
                numRun.setText("{{player" + playerNum + "Number}}");
                numRun.setBold(true);
                
                // 添加球员照片
                XWPFParagraph photoPara = cell.addParagraph();
                photoPara.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun photoRun = photoPara.createRun();
                photoRun.setText("{{@player" + playerNum + "Photo}}");
                
                // 添加球员姓名
                XWPFParagraph namePara = cell.addParagraph();
                namePara.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun nameRun = namePara.createRun();
                nameRun.setText("{{player" + playerNum + "Name}}");
            }
        }
    }
}
