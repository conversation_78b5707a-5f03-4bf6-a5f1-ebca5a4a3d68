import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.util.TableTools;
import java.util.*;
import java.io.IOException;

/**
 * 足球报名表生成器
 * 基于您现有的Word文档生成完整的报名表
 */
public class FootballRegistrationApp {
    
    public static void main(String[] args) {
        try {
            // 生成报名表
            generateRegistrationForm();
            System.out.println("✅ 报名表生成成功！");
            System.out.println("📄 输出文件：淄川区2025年五人制足球比赛报名表_完整版.docx");
        } catch (Exception e) {
            System.err.println("❌ 生成失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 生成足球报名表
     */
    public static void generateRegistrationForm() throws IOException {
        // 1. 准备数据
        Map<String, Object> data = prepareFormData();
        
        // 2. 渲染模板
        XWPFTemplate.compile("football_template.docx")
            .render(data)
            .writeToFile("淄川区2025年五人制足球比赛报名表_完整版.docx");
    }
    
    /**
     * 准备表单数据
     */
    private static Map<String, Object> prepareFormData() {
        Map<String, Object> data = new HashMap<>();
        
        // 基本信息
        data.put("title", "淄川区2025年五人制足球比赛报名表");
        data.put("organizationName", "太河镇人民政府");
        data.put("teamLeader", "张三");
        data.put("coach", "李四");
        data.put("jerseyColor", "蓝色");
        
        // 创建完整的球员表格
        data.put("playerTable", createCompletePlayerTable());
        
        return data;
    }
    
    /**
     * 创建完整的球员信息表格
     */
    private static TableRenderData createCompletePlayerTable() {
        List<RowRenderData> rows = new ArrayList<>();
        
        // 创建表头
        rows.add(createTableHeader());
        
        // 创建球员行（3行，每行5个球员）
        rows.add(createPlayerRow("10", "张雷", "0", "白浩", "3", "翟召昌", "11", "赵飞", "5", "王洪艺"));
        rows.add(createPlayerPhotoRow("player10.jpg", "player0.jpg", "player3.jpg", "player11.jpg", "player5.jpg"));
        
        rows.add(createPlayerRow("6", "霍乃峰", "7", "马香海", "8", "张继姿", "9", "王强", "1", "彭浩"));
        rows.add(createPlayerPhotoRow("player6.jpg", "player7.jpg", "player8.jpg", "player9.jpg", "player1.jpg"));
        
        rows.add(createPlayerRow("", "", "", "", "", "", "", "", "", ""));
        rows.add(createPlayerPhotoRow("", "", "", "", ""));
        
        // 构建表格
        TableBuilder tableBuilder = Tables.ofA4Width();
        for (RowRenderData row : rows) {
            tableBuilder.addRow(row);
        }
        
        return tableBuilder.create();
    }
    
    /**
     * 创建表头
     */
    private static RowRenderData createTableHeader() {
        return Rows.of("", "", "", "", "")
            .bgColor("D3D3D3")
            .center()
            .rowHeight(1.0f)
            .create();
    }
    
    /**
     * 创建球员信息行（号码+姓名）
     */
    private static RowRenderData createPlayerRow(String... playerInfo) {
        List<String> cellData = new ArrayList<>();
        
        // 每两个参数组成一个单元格内容（号码+姓名）
        for (int i = 0; i < playerInfo.length; i += 2) {
            if (i + 1 < playerInfo.length) {
                String number = playerInfo[i];
                String name = playerInfo[i + 1];
                if (!number.isEmpty() && !name.isEmpty()) {
                    cellData.add(number + "号 " + name);
                } else {
                    cellData.add("");
                }
            }
        }
        
        return Rows.of(cellData.toArray(new String[0]))
            .center()
            .rowHeight(1.5f)
            .create();
    }
    
    /**
     * 创建球员照片行
     */
    private static RowRenderData createPlayerPhotoRow(String... photoFiles) {
        List<CellRenderData> cells = new ArrayList<>();
        
        for (String photoFile : photoFiles) {
            CellBuilder cellBuilder = Cells.of();
            
            if (!photoFile.isEmpty()) {
                // 如果有照片文件，插入图片
                try {
                    PictureRenderData photo = Pictures.ofLocal("photos/" + photoFile)
                        .size(60, 80)
                        .center()
                        .create();
                    cellBuilder.addParagraph(Paragraphs.of(photo).center().create());
                } catch (Exception e) {
                    // 如果图片不存在，显示占位文本
                    cellBuilder.addParagraph(Paragraphs.of("照片").center().create());
                }
            } else {
                // 空白单元格
                cellBuilder.addParagraph(Paragraphs.of("").create());
            }
            
            cells.add(cellBuilder.center().create());
        }
        
        return Rows.of(cells.toArray(new CellRenderData[0]))
            .center()
            .rowHeight(3.0f)
            .create();
    }
    
    /**
     * 创建示例球员数据（用于测试）
     */
    public static List<Player> createSamplePlayers() {
        List<Player> players = Arrays.asList(
            new Player("10", "张雷", "photos/player10.jpg"),
            new Player("0", "白浩", "photos/player0.jpg"),
            new Player("3", "翟召昌", "photos/player3.jpg"),
            new Player("11", "赵飞", "photos/player11.jpg"),
            new Player("5", "王洪艺", "photos/player5.jpg"),
            new Player("6", "霍乃峰", "photos/player6.jpg"),
            new Player("7", "马香海", "photos/player7.jpg"),
            new Player("8", "张继姿", "photos/player8.jpg"),
            new Player("9", "王强", "photos/player9.jpg"),
            new Player("1", "彭浩", "photos/player1.jpg")
        );
        return players;
    }
    
    /**
     * 球员信息类
     */
    public static class Player {
        private String number;
        private String name;
        private String photoPath;
        
        public Player(String number, String name, String photoPath) {
            this.number = number;
            this.name = name;
            this.photoPath = photoPath;
        }
        
        // Getters
        public String getNumber() { return number; }
        public String getName() { return name; }
        public String getPhotoPath() { return photoPath; }
    }
}
