/*
 * XML Type:  ST_SecondPieSizeUShort
 * Namespace: http://schemas.openxmlformats.org/drawingml/2006/chart
 * Java type: org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizeUShort
 *
 * Automatically generated - do not modify.
 */
package org.openxmlformats.schemas.drawingml.x2006.chart.impl;

import javax.xml.namespace.QName;
import org.apache.xmlbeans.QNameSet;

/**
 * An XML ST_SecondPieSizeUShort(@http://schemas.openxmlformats.org/drawingml/2006/chart).
 *
 * This is an atomic type that is a restriction of org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizeUShort.
 */
public class STSecondPieSizeUShortImpl extends org.apache.xmlbeans.impl.values.JavaIntHolderEx implements org.openxmlformats.schemas.drawingml.x2006.chart.STSecondPieSizeUShort {
    private static final long serialVersionUID = 1L;

    public STSecondPieSizeUShortImpl(org.apache.xmlbeans.SchemaType sType) {
        super(sType, false);
    }

    protected STSecondPieSizeUShortImpl(org.apache.xmlbeans.SchemaType sType, boolean b) {
        super(sType, b);
    }
}
