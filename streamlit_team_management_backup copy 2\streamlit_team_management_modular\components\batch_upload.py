#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量上传组件
Batch Upload Component

提供批量上传球员照片的UI组件
"""

import streamlit as st
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from services.player_service import PlayerService
from services.photo_service import PhotoService
from utils.safe_file_manager import safe_file_manager
from config.constants import ProcessOptions, UIConstants, FileTypes
from utils.validation import DataValidator

# 配置日志
logger = logging.getLogger(__name__)


class BatchUploadComponent:
    """批量上传组件"""

    def __init__(self):
        self.player_service = PlayerService()
        # 获取当前用户ID用于PhotoService
        user_id = self._get_current_user_id()
        self.photo_service = PhotoService(user_id)

    def _get_current_user_id(self) -> str:
        """获取当前用户ID"""
        return st.session_state.get('user_id', '')
    
    def render_upload_interface(self) -> Optional[List]:
        """
        渲染上传界面

        Returns:
            Optional[List]: 上传的文件列表，如果没有返回None
        """
        st.subheader("📤 批量上传球员照片")
        st.markdown("一次性上传所有球员照片，然后为每张照片添加球员信息")

        # 初始化批量上传key
        if 'batch_upload_key' not in st.session_state:
            import time
            st.session_state.batch_upload_key = str(int(time.time()))

        # 文件上传组件
        uploaded_files = st.file_uploader(
            "选择多张球员照片",
            type=['png', 'jpg', 'jpeg', 'gif', 'bmp'],
            accept_multiple_files=True,
            key=f"batch_upload_{st.session_state.batch_upload_key}"
        )

        # 添加使用说明
        if not uploaded_files:
            st.markdown(
                """
                <div style="background-color: #e8f4fd; padding: 12px; border-radius: 8px; border-left: 4px solid #1f77b4;">
                    <h4 style="margin: 0 0 8px 0; color: #1f77b4;">📸 批量上传说明：</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li><strong>多选文件：</strong> 按住 Ctrl (Windows) 或 Cmd (Mac) 选择多张照片</li>
                        <li><strong>拖拽上传：</strong> 可以直接拖拽多张图片到上传区域</li>
                        <li><strong>支持格式：</strong> PNG, JPG, JPEG, GIF, BMP</li>
                        <li><strong>建议：</strong> 每张照片小于10MB，总数不超过20张</li>
                    </ul>
                </div>
                """,
                unsafe_allow_html=True
            )

            # 添加文件选择器状态提示
            st.markdown(
                """
                <div style="background-color: #fff3cd; padding: 12px; border-radius: 8px; border-left: 4px solid #ffc107; margin-top: 10px;">
                    <h4 style="margin: 0 0 8px 0; color: #856404;">⚠️ 如果点击"Browse files"没有反应：</h4>
                    <ul style="margin: 0; padding-left: 20px;">
                        <li><strong>检查任务栏：</strong> 文件选择器可能在后台打开，查看任务栏是否有闪烁图标</li>
                        <li><strong>按Alt+Tab：</strong> 查看所有打开的窗口，文件选择器可能被遮挡</li>
                        <li><strong>检查其他屏幕：</strong> 如果有多个显示器，文件选择器可能在其他屏幕上</li>
                        <li><strong>使用拖拽：</strong> 直接拖拽文件到上方的上传区域</li>
                        <li><strong>尝试其他浏览器：</strong> Chrome、Firefox或Edge的无痕模式</li>
                    </ul>
                </div>
                """,
                unsafe_allow_html=True
            )

            # 添加故障排除
            with st.expander("🔧 上传按钮无反应？点击查看解决方案"):
                st.markdown(
                    """
                    **常见解决方案：**
                    1. **刷新页面**：按 F5 刷新浏览器
                    2. **清除缓存**：按 Ctrl+Shift+R 强制刷新
                    3. **减少文件数量**：一次上传少于10张照片
                    4. **检查文件大小**：确保每张照片小于10MB
                    5. **使用拖拽**：直接拖拽文件到上传区域
                    """
                )
        
        # 添加中文提示和英文说明
        if not uploaded_files:
            st.info("💡 提示：点击上方区域或拖拽照片文件到此处进行上传")
            st.markdown(
                f"""
                <div style="background-color: #f0f2f6; padding: 10px; border-radius: 5px; margin-top: 10px;">
                    <small>{UIConstants.HELP_TEXTS["english_ui"]}</small>
                </div>
                """, 
                unsafe_allow_html=True
            )
            
            if st.button("❌ 取消批量上传"):
                st.session_state.batch_mode = 'normal'
                st.rerun()
            
            return None
        
        st.success(f"已选择 {len(uploaded_files)} 张照片")
        
        # 显示照片预览
        st.markdown("### 照片预览")
        cols_per_row = min(len(uploaded_files), 3)  # 最多3列
        cols = st.columns(cols_per_row)

        for i, uploaded_file in enumerate(uploaded_files):
            with cols[i % cols_per_row]:
                # 使用安全的图片显示方法
                safe_file_manager.safe_image_display(
                    uploaded_file,
                    caption=f"照片 {i+1}",
                    width=200,
                    fallback_message="照片预览失败"
                )
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("✅ 确认上传，开始添加球员信息", type="primary"):
                with st.spinner('📸 正在处理照片...'):
                    st.session_state.batch_photos = uploaded_files
                    st.session_state.batch_mode = 'edit'
                st.rerun()

        with col2:
            if st.button("❌ 取消批量上传"):
                st.session_state.batch_mode = 'normal'
                st.session_state.batch_photos = []
                st.rerun()
        
        return uploaded_files

    def render_batch_settings(self, num_photos: int) -> None:
        """
        渲染批量设置区域

        Args:
            num_photos: 照片数量
        """
        st.markdown("### ⚡ 批量设置处理方案")
        st.markdown("*快速为所有球员设置相同的处理方案，然后可以单独调整*")

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("🔄🖼️⚪ 全部设为全套处理", key="batch_full"):
                for i in range(num_photos):
                    st.session_state[f"batch_process_{i}"] = "全套处理"
                st.rerun()

        with col2:
            if st.button("🖼️⚪ 全部设为背景去除+白底", key="batch_bg_white"):
                for i in range(num_photos):
                    st.session_state[f"batch_process_{i}"] = "背景去除+白底"
                st.rerun()

        with col3:
            if st.button("⚪ 全部设为仅白底", key="batch_white"):
                for i in range(num_photos):
                    st.session_state[f"batch_process_{i}"] = "仅白底"
                st.rerun()

        with col4:
            if st.button("🚫 全部设为不处理", key="batch_none"):
                for i in range(num_photos):
                    st.session_state[f"batch_process_{i}"] = "不处理"
                st.rerun()

    def render_template_upload(self) -> Optional[Any]:
        """
        渲染模板图上传区域

        Returns:
            Optional[Any]: 上传的模板文件
        """
        st.markdown("### 🎨 模板图上传（可选）")
        st.markdown("*如果需要为球员进行换装处理，请上传模板图*")

        clothes_image = st.file_uploader(
            "选择模板图片（换装时需要）",
            type=['png', 'jpg', 'jpeg'],
            help=UIConstants.HELP_TEXTS["template_upload"]
        )

        if clothes_image:
            col1, col2 = st.columns([1, 2])
            with col1:
                st.image(clothes_image, caption="模板图预览", width=200)
            with col2:
                st.info("✅ 模板图已选择")
                st.markdown("**模板图说明：**")
                st.markdown("- 🎽 **球衣模板**：用于统一球员球衣样式")
                st.markdown("- 👕 **服装模板**：用于更换球员服装风格")
                st.markdown("- 🎨 **设计模板**：用于展示不同设计效果")

        return clothes_image

    def render_batch_edit_form(self, team_name: str, photos: List,
                              existing_players: List) -> bool:
        """
        渲染批量编辑表单

        Args:
            team_name: 球队名称
            photos: 照片列表
            existing_players: 现有球员列表

        Returns:
            bool: 是否成功保存
        """
        st.subheader("✏️ 球员信息标注 + 处理配置")
        st.markdown("为每张照片填写球员信息，并设置个性化处理方案")

        # 模板图上传
        clothes_image = self.render_template_upload()

        # 批量设置
        self.render_batch_settings(len(photos))

        with st.form("batch_edit_form"):
            # 为每张照片创建输入区域
            for i, photo in enumerate(photos):
                st.markdown(f"#### 球员 {i+1}")

                # 使用三列布局：照片、基本信息、处理配置
                col1, col2, col3 = st.columns([2, 2, 2])

                with col1:
                    # 照片显示
                    st.image(photo, width=250, caption=f"球员照片 {i+1}")

                with col2:
                    # 基本信息输入
                    st.markdown("**基本信息**")
                    st.text_input(
                        f"球员姓名 *",
                        key=f"name_{i}",
                        placeholder="请输入球员姓名"
                    )

                    # 获取已使用的号码
                    used_numbers = [p.jersey_number for p in existing_players]
                    st.number_input(
                        f"球衣号码 *",
                        min_value=1,
                        max_value=99,
                        value=i+1,
                        key=f"number_{i}",
                        help=f"已使用号码: {', '.join(map(str, used_numbers)) if used_numbers else '无'}"
                    )

                with col3:
                    # 处理方案配置
                    st.markdown("**处理方案**")

                    # 初始化默认处理选项
                    if f"batch_process_{i}" not in st.session_state:
                        st.session_state[f"batch_process_{i}"] = "全套处理"

                    current_option = st.session_state.get(f"batch_process_{i}", "全套处理")

                    st.selectbox(
                        "处理方案",
                        options=list(ProcessOptions.OPTIONS.keys()),
                        index=list(ProcessOptions.OPTIONS.keys()).index(current_option),
                        key=f"batch_process_{i}",
                        format_func=lambda x: ProcessOptions.OPTIONS[x]["label"]
                    )

                    # 显示选项说明
                    current_process_option = st.session_state.get(f"batch_process_{i}", "全套处理")
                    option_info = ProcessOptions.OPTIONS[current_process_option]
                    st.markdown(f"**说明：** {option_info['description']}")

                st.divider()

            return self._render_form_validation_and_submit(
                team_name, photos, clothes_image
            )

    def _render_form_validation_and_submit(self, team_name: str, photos: List,
                                         clothes_image) -> bool:
        """
        渲染表单验证和提交区域

        Args:
            team_name: 球队名称
            photos: 照片列表
            clothes_image: 模板图片

        Returns:
            bool: 是否成功保存
        """
        # 处理统计
        st.markdown("### 📊 处理统计")
        st.markdown("**处理方案统计：**")

        # 统计当前处理方案
        process_stats = {}
        for i, photo in enumerate(photos):
            option = st.session_state.get(f"batch_process_{i}", "全套处理")
            process_stats[option] = process_stats.get(option, 0) + 1

        for option, count in process_stats.items():
            if count > 0:
                option_info = ProcessOptions.OPTIONS[option]
                st.markdown(f"- {option_info['label']}: {count}人")

        # 验证和保存
        st.markdown("### 💾 保存配置")

        # 批量保存按钮
        col1, col2 = st.columns(2)
        with col1:
            save_players = st.form_submit_button("💾 保存球员信息", type="primary")

        with col2:
            cancel = st.form_submit_button("❌ 取消")

        if save_players:
            # 在表单提交后收集和验证数据
            return self._process_batch_save_with_validation(team_name, photos, clothes_image, False)

        if cancel:
            self._cleanup_session_state(len(photos))
            st.session_state.batch_mode = 'normal'
            st.session_state.batch_photos = []
            st.rerun()

        return False

    def _process_batch_save_with_validation(self, team_name: str, photos: List,
                                          clothes_image, save_and_process: bool) -> bool:
        """
        表单提交后的数据收集、验证和保存
        """
        # 从session_state收集球员数据
        player_data = []
        needs_template = False

        for i, photo in enumerate(photos):
            # 从session_state获取用户输入的数据
            name = st.session_state.get(f"name_{i}", "").strip()
            jersey_number = st.session_state.get(f"number_{i}", i+1)
            process_option = st.session_state.get(f"batch_process_{i}", "全套处理")

            # 检查是否需要模板图
            option_info = ProcessOptions.OPTIONS[process_option]
            if option_info["fashion"]:
                needs_template = True

            player_data.append({
                'name': name,
                'jersey_number': jersey_number,
                'photo': photo,
                'process_option': process_option
            })

        # 验证条件
        validation_errors = []

        # 检查基本信息
        for i, data in enumerate(player_data):
            if not data['name']:
                validation_errors.append(f"球员 {i+1} 缺少姓名")

        # 检查号码重复
        jersey_numbers = [data['jersey_number'] for data in player_data if data['name']]
        if len(jersey_numbers) != len(set(jersey_numbers)):
            validation_errors.append("存在重复的球衣号码")

        # 检查模板图
        if needs_template and not clothes_image:
            validation_errors.append("有球员需要换装处理，请上传模板图")

        # 如果有验证错误，显示错误并返回
        if validation_errors:
            for error in validation_errors:
                st.error(f"❌ {error}")
            return False

        # 验证通过，调用原来的保存方法
        return self._process_batch_save(team_name, player_data, clothes_image, save_and_process)

    def _process_batch_save(self, team_name: str, player_data: List[Dict],
                           clothes_image, save_and_process: bool) -> bool:
        """
        处理批量保存

        Args:
            team_name: 球队名称
            player_data: 球员数据列表
            clothes_image: 模板图片
            save_and_process: 是否同时开始处理

        Returns:
            bool: 是否成功
        """
        success_count = 0
        error_messages = []
        saved_players = []

        # 保存模板图片（如果有）
        clothes_image_path = None
        if clothes_image:
            clothes_image_path = self.photo_service.file_manager.save_template_image(
                clothes_image, f"batch_template_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            )

        # 保存球员信息
        for i, data in enumerate(player_data):
            if data['name'].strip():
                try:
                    success, message = self.player_service.add_player(
                        team_name, data['name'], str(data['jersey_number']), data['photo']
                    )

                    if success:
                        success_count += 1
                        # 保存处理配置到session state
                        player_id = f"{data['name']}_{data['jersey_number']}"
                        saved_players.append({
                            'name': data['name'],
                            'jersey_number': data['jersey_number'],
                            'process_option': data['process_option'],
                            'player_id': player_id
                        })
                    else:
                        error_messages.append(f"球员{i+1}: {message}")
                except Exception as e:
                    error_messages.append(f"球员{i+1}: {str(e)}")
            else:
                error_messages.append(f"球员{i+1}: 请输入球员姓名")

        if success_count > 0:
            st.success(f"✅ 成功添加 {success_count} 名球员！")

            # 显示处理配置信息
            st.info("💡 球员信息和处理配置已保存，您可以在AI聊天中进行换装处理！")

            # 显示处理配置摘要
            with st.expander("查看处理配置摘要", expanded=False):
                for player in saved_players:
                    option_info = ProcessOptions.OPTIONS[player['process_option']]
                    st.write(f"**{player['name']} (#{player['jersey_number']})**: {option_info['label']}")
                    st.caption(option_info['description'])

        if error_messages:
            for msg in error_messages:
                st.error(msg)

        if success_count == len([d for d in player_data if d['name'].strip()]):
            # 清理session state
            self._cleanup_session_state(len(player_data))
            st.session_state.batch_mode = 'normal'
            st.session_state.batch_photos = []
            st.rerun()
            return True

        return False



    def _cleanup_session_state(self, num_photos: int) -> None:
        """
        清理session state

        Args:
            num_photos: 照片数量
        """
        for i in range(num_photos):
            if f"batch_process_{i}" in st.session_state:
                del st.session_state[f"batch_process_{i}"]
