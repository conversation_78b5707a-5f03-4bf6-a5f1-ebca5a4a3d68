package com.deepoove.poi.tl.source;

import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.expression.Name;

public class ComplexVOTest {
    @Name("d_number")
    private String dNumber;
    @Name("d_obj")
    private String dObj;
    @Name("m_vin")
    private String mVin;
    @Name("d_date")
    private String dDate;
    @Name("d_man")
    private String dMan;
    @Name("d_accident")
    private String dAccident;
    @Name("d_safeWord")
    private String dSafeWord;
    @Name("d_operWord")
    private String dOperWord;
    @Name("d_cfgWord")
    private String dCfgWord;
    @Name("d_depart")
    private String dDepart;
    private String A;
    private String metalNumber;
    private String metalWord;
    private PictureRenderData metalPicture;
    @Name("s_safeWord")
    private String sSafeWord;
    @Name("s_treadWord")
    private String sTreadWord;
    @Name("s_brakeWord")
    private String sBrakeWord;
    private PictureRenderData treadPicture;
    private PictureRenderData brakePicture;
    @Name("s_fluidWord")
    private String sFluidWord;
    @Name("s_antiWord")
    private String sAntiWord;
    private PictureRenderData fluidPicture;
    private PictureRenderData antiPicture;
    private String zhidong;
    @Name("detect_1")
    private String detect1;
    @Name("detect_1pic")
    private PictureRenderData detect1pic;
    @Name("detect_1pro")
    private String detect1pro;
    @Name("detect_3")
    private String detect3;
    @Name("detect_3pic")
    private PictureRenderData detect3pic;
    @Name("detect_3pro")
    private String detect3pro;
    @Name("detect_4")
    private String detect4;
    @Name("detect_4pic")
    private PictureRenderData detect4pic;
    @Name("detect_4pro")
    private String detect4pro;
    @Name("o_operWord")
    private String oOperWord;
    @Name("detect_16")
    private String detect16;
    @Name("detect_16pic")
    private PictureRenderData detect16pic;
    @Name("detect_16pro")
    private String detect16pro;
    @Name("detect_17")
    private String detect17;
    @Name("detect_17pic")
    private PictureRenderData detect17pic;
    @Name("detect_17pro")
    private String detect17pro;
    @Name("detect_18")
    private String detect18;
    @Name("detect_18pic")
    private PictureRenderData detect18pic;
    @Name("detect_18pro")
    private String detect18pro;
    @Name("detect_15pic")
    private PictureRenderData detect15pic;
    @Name("detect_15")
    private String detect15;
    @Name("detect_15pro")
    private String detect15pro;
    private String qidong;
    private String jiasu;
    @Name("detect_19")
    private String detect19;
    @Name("detect_19pic")
    private PictureRenderData detect19pic;
    @Name("detect_19pro")
    private String detect19pro;
    @Name("detect_20")
    private String detect20;
    @Name("detect_20pic")
    private PictureRenderData detect20pic;
    @Name("detect_20pro")
    private String detect20pro;
    @Name("detect_21")
    private String detect21;
    @Name("detect_21pic")
    private PictureRenderData detect21pic;
    @Name("detect_21pro")
    private String detect21pro;
    private String yunsu;
    @Name("detect_22")
    private String detect22;
    @Name("detect_22pic")
    private PictureRenderData detect22pic;
    @Name("detect_22pro")
    private String detect22pro;
    @Name("detect_5")
    private String detect5;
    @Name("detect_5pic")
    private PictureRenderData detect5pic;
    @Name("detect_5pro")
    private String detect5pro;
    @Name("detect_6")
    private String detect6;
    @Name("detect_6pic")
    private PictureRenderData detect6pic;
    @Name("detect_6pro")
    private String detect6pro;
    @Name("detect_7pic")
    private PictureRenderData detect7pic;
    @Name("detect_7")
    private String detect7;
    @Name("detect_7pro")
    private String detect7pro;
    @Name("detect_8pic")
    private PictureRenderData detect8pic;
    @Name("detect_8")
    private String detect8;
    @Name("detect_8pro")
    private String detect8pro;
    @Name("detect_9")
    private String detect9;
    @Name("detect_9pic")
    private PictureRenderData detect9pic;
    @Name("detect_9pro")
    private String detect9pro;
    @Name("detect_13")
    private String detect13;
    @Name("detect_13pic")
    private PictureRenderData detect13pic;
    @Name("detect_13pro")
    private String detect13pro;
    @Name("detect_14")
    private String detect14;
    @Name("detect_14pic")
    private PictureRenderData detect14pic;
    @Name("detect_14pro")
    private String detect14pro;
    @Name("detect_10")
    private String detect10;
    @Name("detect_10pic")
    private PictureRenderData detect10pic;
    @Name("detect_10pro")
    private String detect10pro;
    @Name("detect_11pic")
    private PictureRenderData detect11pic;
    @Name("detect_11")
    private String detect11;
    @Name("detect_11pro")
    private String detect11pro;
    @Name("detect_12pic")
    private PictureRenderData detect12pic;
    @Name("detect_12")
    private String detect12;
    @Name("detect_12pro")
    private String detect12pro;
    private String zhxiang;
    @Name("c_cfgWord")
    private String cCfgWord;
    @Name("fbsz_v")
    private PictureRenderData fbszV;
    @Name("fbsz_p")
    private PictureRenderData fbszP;
    @Name("qjtc_v")
    private PictureRenderData qjtcV;
    @Name("qjtc_p")
    private PictureRenderData qjtcP;
    @Name("czld_v")
    private PictureRenderData czldV;
    @Name("czld_p")
    private PictureRenderData czldP;
    @Name("ddtc_v")
    private PictureRenderData ddtcV;
    @Name("ddtc_p")
    private PictureRenderData ddtcP;
    @Name("zpzy_v")
    private PictureRenderData zpzyV;
    @Name("zpzy_p")
    private PictureRenderData zpzyP;
    @Name("qhdd_v")
    private PictureRenderData qhddV;
    @Name("qhdd_p")
    private PictureRenderData qhddP;
    @Name("ddxt_v")
    private PictureRenderData ddxtV;
    @Name("ddxt_p")
    private PictureRenderData ddxtP;
    @Name("ddzd_v")
    private PictureRenderData ddzdV;
    @Name("ddzd_p")
    private PictureRenderData ddzdP;
    @Name("sqdd_v")
    private PictureRenderData sqddV;
    @Name("sqdd_p")
    private PictureRenderData sqddP;
    @Name("aqql_v")
    private PictureRenderData aqqlV;
    @Name("aqql_p")
    private PictureRenderData aqqlP;
    @Name("dvdd_v")
    private PictureRenderData dvddV;
    @Name("dvdd_p")
    private PictureRenderData dvddP;
    @Name("zdhw_v")
    private PictureRenderData zdhwV;
    @Name("zdhw_p")
    private PictureRenderData zdhwP;
    @Name("dcyx_v")
    private PictureRenderData dcyxV;
    @Name("dcyx_p")
    private PictureRenderData dcyxP;
    @Name("yjqd_v")
    private PictureRenderData yjqdV;
    @Name("yjqd_p")
    private PictureRenderData yjqdP;
    @Name("ddtj_v")
    private PictureRenderData ddtjV;
    @Name("ddtj_p")
    private PictureRenderData ddtjP;
    @Name("dsxh_v")
    private PictureRenderData dsxhV;
    @Name("dsxh_p")
    private PictureRenderData dsxhP;
    @Name("m_plate")
    private String mPlate;
    @Name("m_engin")
    private String mEngin;
    @Name("m_firstp")
    private String mFirstp;
    @Name("m_insure")
    private String mInsure;
    @Name("m_annual")
    private String mAnnual;
    @Name("m_mile")
    private String mMile;
    @Name("m_key")
    private String mKey;

    public void setDNumber(String dNumber) {
        this.dNumber = dNumber;
    }

    public String getDNumber() {
        return this.dNumber;
    }

    public void setDObj(String dObj) {
        this.dObj = dObj;
    }

    public String getDObj() {
        return this.dObj;
    }

    public void setMVin(String mVin) {
        this.mVin = mVin;
    }

    public String getMVin() {
        return this.mVin;
    }

    public void setDDate(String dDate) {
        this.dDate = dDate;
    }

    public String getDDate() {
        return this.dDate;
    }

    public void setDMan(String dMan) {
        this.dMan = dMan;
    }

    public String getDMan() {
        return this.dMan;
    }

    public void setDAccident(String dAccident) {
        this.dAccident = dAccident;
    }

    public String getDAccident() {
        return this.dAccident;
    }

    public void setDSafeWord(String dSafeWord) {
        this.dSafeWord = dSafeWord;
    }

    public String getDSafeWord() {
        return this.dSafeWord;
    }

    public void setDOperWord(String dOperWord) {
        this.dOperWord = dOperWord;
    }

    public String getDOperWord() {
        return this.dOperWord;
    }

    public void setDCfgWord(String dCfgWord) {
        this.dCfgWord = dCfgWord;
    }

    public String getDCfgWord() {
        return this.dCfgWord;
    }

    public void setDDepart(String dDepart) {
        this.dDepart = dDepart;
    }

    public String getDDepart() {
        return this.dDepart;
    }

    public void setA(String A) {
        this.A = A;
    }

    public String getA() {
        return this.A;
    }

    public void setMetalNumber(String metalNumber) {
        this.metalNumber = metalNumber;
    }

    public String getMetalNumber() {
        return this.metalNumber;
    }

    public void setMetalWord(String metalWord) {
        this.metalWord = metalWord;
    }

    public String getMetalWord() {
        return this.metalWord;
    }

    public void setMetalPicture(PictureRenderData metalPicture) {
        this.metalPicture = metalPicture;
    }

    public PictureRenderData getMetalPicture() {
        return this.metalPicture;
    }

    public void setSSafeWord(String sSafeWord) {
        this.sSafeWord = sSafeWord;
    }

    public String getSSafeWord() {
        return this.sSafeWord;
    }

    public void setSTreadWord(String sTreadWord) {
        this.sTreadWord = sTreadWord;
    }

    public String getSTreadWord() {
        return this.sTreadWord;
    }

    public void setSBrakeWord(String sBrakeWord) {
        this.sBrakeWord = sBrakeWord;
    }

    public String getSBrakeWord() {
        return this.sBrakeWord;
    }

    public void setTreadPicture(PictureRenderData treadPicture) {
        this.treadPicture = treadPicture;
    }

    public PictureRenderData getTreadPicture() {
        return this.treadPicture;
    }

    public void setBrakePicture(PictureRenderData brakePicture) {
        this.brakePicture = brakePicture;
    }

    public PictureRenderData getBrakePicture() {
        return this.brakePicture;
    }

    public void setSFluidWord(String sFluidWord) {
        this.sFluidWord = sFluidWord;
    }

    public String getSFluidWord() {
        return this.sFluidWord;
    }

    public void setSAntiWord(String sAntiWord) {
        this.sAntiWord = sAntiWord;
    }

    public String getSAntiWord() {
        return this.sAntiWord;
    }

    public void setFluidPicture(PictureRenderData fluidPicture) {
        this.fluidPicture = fluidPicture;
    }

    public PictureRenderData getFluidPicture() {
        return this.fluidPicture;
    }

    public void setAntiPicture(PictureRenderData antiPicture) {
        this.antiPicture = antiPicture;
    }

    public PictureRenderData getAntiPicture() {
        return this.antiPicture;
    }

    public void setZhidong(String zhidong) {
        this.zhidong = zhidong;
    }

    public String getZhidong() {
        return this.zhidong;
    }

    public void setDetect1(String detect1) {
        this.detect1 = detect1;
    }

    public String getDetect1() {
        return this.detect1;
    }

    public void setDetect1pic(PictureRenderData detect1pic) {
        this.detect1pic = detect1pic;
    }

    public PictureRenderData getDetect1pic() {
        return this.detect1pic;
    }

    public void setDetect1pro(String detect1pro) {
        this.detect1pro = detect1pro;
    }

    public String getDetect1pro() {
        return this.detect1pro;
    }

    public void setDetect3(String detect3) {
        this.detect3 = detect3;
    }

    public String getDetect3() {
        return this.detect3;
    }

    public void setDetect3pic(PictureRenderData detect3pic) {
        this.detect3pic = detect3pic;
    }

    public PictureRenderData getDetect3pic() {
        return this.detect3pic;
    }

    public void setDetect3pro(String detect3pro) {
        this.detect3pro = detect3pro;
    }

    public String getDetect3pro() {
        return this.detect3pro;
    }

    public void setDetect4(String detect4) {
        this.detect4 = detect4;
    }

    public String getDetect4() {
        return this.detect4;
    }

    public void setDetect4pic(PictureRenderData detect4pic) {
        this.detect4pic = detect4pic;
    }

    public PictureRenderData getDetect4pic() {
        return this.detect4pic;
    }

    public void setDetect4pro(String detect4pro) {
        this.detect4pro = detect4pro;
    }

    public String getDetect4pro() {
        return this.detect4pro;
    }

    public void setOOperWord(String oOperWord) {
        this.oOperWord = oOperWord;
    }

    public String getOOperWord() {
        return this.oOperWord;
    }

    public void setDetect16(String detect16) {
        this.detect16 = detect16;
    }

    public String getDetect16() {
        return this.detect16;
    }

    public void setDetect16pic(PictureRenderData detect16pic) {
        this.detect16pic = detect16pic;
    }

    public PictureRenderData getDetect16pic() {
        return this.detect16pic;
    }

    public void setDetect16pro(String detect16pro) {
        this.detect16pro = detect16pro;
    }

    public String getDetect16pro() {
        return this.detect16pro;
    }

    public void setDetect17(String detect17) {
        this.detect17 = detect17;
    }

    public String getDetect17() {
        return this.detect17;
    }

    public void setDetect17pic(PictureRenderData detect17pic) {
        this.detect17pic = detect17pic;
    }

    public PictureRenderData getDetect17pic() {
        return this.detect17pic;
    }

    public void setDetect17pro(String detect17pro) {
        this.detect17pro = detect17pro;
    }

    public String getDetect17pro() {
        return this.detect17pro;
    }

    public void setDetect18(String detect18) {
        this.detect18 = detect18;
    }

    public String getDetect18() {
        return this.detect18;
    }

    public void setDetect18pic(PictureRenderData detect18pic) {
        this.detect18pic = detect18pic;
    }

    public PictureRenderData getDetect18pic() {
        return this.detect18pic;
    }

    public void setDetect18pro(String detect18pro) {
        this.detect18pro = detect18pro;
    }

    public String getDetect18pro() {
        return this.detect18pro;
    }

    public void setDetect15pic(PictureRenderData detect15pic) {
        this.detect15pic = detect15pic;
    }

    public PictureRenderData getDetect15pic() {
        return this.detect15pic;
    }

    public void setDetect15(String detect15) {
        this.detect15 = detect15;
    }

    public String getDetect15() {
        return this.detect15;
    }

    public void setDetect15pro(String detect15pro) {
        this.detect15pro = detect15pro;
    }

    public String getDetect15pro() {
        return this.detect15pro;
    }

    public void setQidong(String qidong) {
        this.qidong = qidong;
    }

    public String getQidong() {
        return this.qidong;
    }

    public void setJiasu(String jiasu) {
        this.jiasu = jiasu;
    }

    public String getJiasu() {
        return this.jiasu;
    }

    public void setDetect19(String detect19) {
        this.detect19 = detect19;
    }

    public String getDetect19() {
        return this.detect19;
    }

    public void setDetect19pic(PictureRenderData detect19pic) {
        this.detect19pic = detect19pic;
    }

    public PictureRenderData getDetect19pic() {
        return this.detect19pic;
    }

    public void setDetect19pro(String detect19pro) {
        this.detect19pro = detect19pro;
    }

    public String getDetect19pro() {
        return this.detect19pro;
    }

    public void setDetect20(String detect20) {
        this.detect20 = detect20;
    }

    public String getDetect20() {
        return this.detect20;
    }

    public void setDetect20pic(PictureRenderData detect20pic) {
        this.detect20pic = detect20pic;
    }

    public PictureRenderData getDetect20pic() {
        return this.detect20pic;
    }

    public void setDetect20pro(String detect20pro) {
        this.detect20pro = detect20pro;
    }

    public String getDetect20pro() {
        return this.detect20pro;
    }

    public void setDetect21(String detect21) {
        this.detect21 = detect21;
    }

    public String getDetect21() {
        return this.detect21;
    }

    public void setDetect21pic(PictureRenderData detect21pic) {
        this.detect21pic = detect21pic;
    }

    public PictureRenderData getDetect21pic() {
        return this.detect21pic;
    }

    public void setDetect21pro(String detect21pro) {
        this.detect21pro = detect21pro;
    }

    public String getDetect21pro() {
        return this.detect21pro;
    }

    public void setYunsu(String yunsu) {
        this.yunsu = yunsu;
    }

    public String getYunsu() {
        return this.yunsu;
    }

    public void setDetect22(String detect22) {
        this.detect22 = detect22;
    }

    public String getDetect22() {
        return this.detect22;
    }

    public void setDetect22pic(PictureRenderData detect22pic) {
        this.detect22pic = detect22pic;
    }

    public PictureRenderData getDetect22pic() {
        return this.detect22pic;
    }

    public void setDetect22pro(String detect22pro) {
        this.detect22pro = detect22pro;
    }

    public String getDetect22pro() {
        return this.detect22pro;
    }

    public void setDetect5(String detect5) {
        this.detect5 = detect5;
    }

    public String getDetect5() {
        return this.detect5;
    }

    public void setDetect5pic(PictureRenderData detect5pic) {
        this.detect5pic = detect5pic;
    }

    public PictureRenderData getDetect5pic() {
        return this.detect5pic;
    }

    public void setDetect5pro(String detect5pro) {
        this.detect5pro = detect5pro;
    }

    public String getDetect5pro() {
        return this.detect5pro;
    }

    public void setDetect6(String detect6) {
        this.detect6 = detect6;
    }

    public String getDetect6() {
        return this.detect6;
    }

    public void setDetect6pic(PictureRenderData detect6pic) {
        this.detect6pic = detect6pic;
    }

    public PictureRenderData getDetect6pic() {
        return this.detect6pic;
    }

    public void setDetect6pro(String detect6pro) {
        this.detect6pro = detect6pro;
    }

    public String getDetect6pro() {
        return this.detect6pro;
    }

    public void setDetect7pic(PictureRenderData detect7pic) {
        this.detect7pic = detect7pic;
    }

    public PictureRenderData getDetect7pic() {
        return this.detect7pic;
    }

    public void setDetect7(String detect7) {
        this.detect7 = detect7;
    }

    public String getDetect7() {
        return this.detect7;
    }

    public void setDetect7pro(String detect7pro) {
        this.detect7pro = detect7pro;
    }

    public String getDetect7pro() {
        return this.detect7pro;
    }

    public void setDetect8pic(PictureRenderData detect8pic) {
        this.detect8pic = detect8pic;
    }

    public PictureRenderData getDetect8pic() {
        return this.detect8pic;
    }

    public void setDetect8(String detect8) {
        this.detect8 = detect8;
    }

    public String getDetect8() {
        return this.detect8;
    }

    public void setDetect8pro(String detect8pro) {
        this.detect8pro = detect8pro;
    }

    public String getDetect8pro() {
        return this.detect8pro;
    }

    public void setDetect9(String detect9) {
        this.detect9 = detect9;
    }

    public String getDetect9() {
        return this.detect9;
    }

    public void setDetect9pic(PictureRenderData detect9pic) {
        this.detect9pic = detect9pic;
    }

    public PictureRenderData getDetect9pic() {
        return this.detect9pic;
    }

    public void setDetect9pro(String detect9pro) {
        this.detect9pro = detect9pro;
    }

    public String getDetect9pro() {
        return this.detect9pro;
    }

    public void setDetect13(String detect13) {
        this.detect13 = detect13;
    }

    public String getDetect13() {
        return this.detect13;
    }

    public void setDetect13pic(PictureRenderData detect13pic) {
        this.detect13pic = detect13pic;
    }

    public PictureRenderData getDetect13pic() {
        return this.detect13pic;
    }

    public void setDetect13pro(String detect13pro) {
        this.detect13pro = detect13pro;
    }

    public String getDetect13pro() {
        return this.detect13pro;
    }

    public void setDetect14(String detect14) {
        this.detect14 = detect14;
    }

    public String getDetect14() {
        return this.detect14;
    }

    public void setDetect14pic(PictureRenderData detect14pic) {
        this.detect14pic = detect14pic;
    }

    public PictureRenderData getDetect14pic() {
        return this.detect14pic;
    }

    public void setDetect14pro(String detect14pro) {
        this.detect14pro = detect14pro;
    }

    public String getDetect14pro() {
        return this.detect14pro;
    }

    public void setDetect10(String detect10) {
        this.detect10 = detect10;
    }

    public String getDetect10() {
        return this.detect10;
    }

    public void setDetect10pic(PictureRenderData detect10pic) {
        this.detect10pic = detect10pic;
    }

    public PictureRenderData getDetect10pic() {
        return this.detect10pic;
    }

    public void setDetect10pro(String detect10pro) {
        this.detect10pro = detect10pro;
    }

    public String getDetect10pro() {
        return this.detect10pro;
    }

    public void setDetect11pic(PictureRenderData detect11pic) {
        this.detect11pic = detect11pic;
    }

    public PictureRenderData getDetect11pic() {
        return this.detect11pic;
    }

    public void setDetect11(String detect11) {
        this.detect11 = detect11;
    }

    public String getDetect11() {
        return this.detect11;
    }

    public void setDetect11pro(String detect11pro) {
        this.detect11pro = detect11pro;
    }

    public String getDetect11pro() {
        return this.detect11pro;
    }

    public void setDetect12pic(PictureRenderData detect12pic) {
        this.detect12pic = detect12pic;
    }

    public PictureRenderData getDetect12pic() {
        return this.detect12pic;
    }

    public void setDetect12(String detect12) {
        this.detect12 = detect12;
    }

    public String getDetect12() {
        return this.detect12;
    }

    public void setDetect12pro(String detect12pro) {
        this.detect12pro = detect12pro;
    }

    public String getDetect12pro() {
        return this.detect12pro;
    }

    public void setZhxiang(String zhxiang) {
        this.zhxiang = zhxiang;
    }

    public String getZhxiang() {
        return this.zhxiang;
    }

    public void setCCfgWord(String cCfgWord) {
        this.cCfgWord = cCfgWord;
    }

    public String getCCfgWord() {
        return this.cCfgWord;
    }

    public void setFbszV(PictureRenderData fbszV) {
        this.fbszV = fbszV;
    }

    public PictureRenderData getFbszV() {
        return this.fbszV;
    }

    public void setFbszP(PictureRenderData fbszP) {
        this.fbszP = fbszP;
    }

    public PictureRenderData getFbszP() {
        return this.fbszP;
    }

    public void setQjtcV(PictureRenderData qjtcV) {
        this.qjtcV = qjtcV;
    }

    public PictureRenderData getQjtcV() {
        return this.qjtcV;
    }

    public void setQjtcP(PictureRenderData qjtcP) {
        this.qjtcP = qjtcP;
    }

    public PictureRenderData getQjtcP() {
        return this.qjtcP;
    }

    public void setCzldV(PictureRenderData czldV) {
        this.czldV = czldV;
    }

    public PictureRenderData getCzldV() {
        return this.czldV;
    }

    public void setCzldP(PictureRenderData czldP) {
        this.czldP = czldP;
    }

    public PictureRenderData getCzldP() {
        return this.czldP;
    }

    public void setDdtcV(PictureRenderData ddtcV) {
        this.ddtcV = ddtcV;
    }

    public PictureRenderData getDdtcV() {
        return this.ddtcV;
    }

    public void setDdtcP(PictureRenderData ddtcP) {
        this.ddtcP = ddtcP;
    }

    public PictureRenderData getDdtcP() {
        return this.ddtcP;
    }

    public void setZpzyV(PictureRenderData zpzyV) {
        this.zpzyV = zpzyV;
    }

    public PictureRenderData getZpzyV() {
        return this.zpzyV;
    }

    public void setZpzyP(PictureRenderData zpzyP) {
        this.zpzyP = zpzyP;
    }

    public PictureRenderData getZpzyP() {
        return this.zpzyP;
    }

    public void setQhddV(PictureRenderData qhddV) {
        this.qhddV = qhddV;
    }

    public PictureRenderData getQhddV() {
        return this.qhddV;
    }

    public void setQhddP(PictureRenderData qhddP) {
        this.qhddP = qhddP;
    }

    public PictureRenderData getQhddP() {
        return this.qhddP;
    }

    public void setDdxtV(PictureRenderData ddxtV) {
        this.ddxtV = ddxtV;
    }

    public PictureRenderData getDdxtV() {
        return this.ddxtV;
    }

    public void setDdxtP(PictureRenderData ddxtP) {
        this.ddxtP = ddxtP;
    }

    public PictureRenderData getDdxtP() {
        return this.ddxtP;
    }

    public void setDdzdV(PictureRenderData ddzdV) {
        this.ddzdV = ddzdV;
    }

    public PictureRenderData getDdzdV() {
        return this.ddzdV;
    }

    public void setDdzdP(PictureRenderData ddzdP) {
        this.ddzdP = ddzdP;
    }

    public PictureRenderData getDdzdP() {
        return this.ddzdP;
    }

    public void setSqddV(PictureRenderData sqddV) {
        this.sqddV = sqddV;
    }

    public PictureRenderData getSqddV() {
        return this.sqddV;
    }

    public void setSqddP(PictureRenderData sqddP) {
        this.sqddP = sqddP;
    }

    public PictureRenderData getSqddP() {
        return this.sqddP;
    }

    public void setAqqlV(PictureRenderData aqqlV) {
        this.aqqlV = aqqlV;
    }

    public PictureRenderData getAqqlV() {
        return this.aqqlV;
    }

    public void setAqqlP(PictureRenderData aqqlP) {
        this.aqqlP = aqqlP;
    }

    public PictureRenderData getAqqlP() {
        return this.aqqlP;
    }

    public void setDvddV(PictureRenderData dvddV) {
        this.dvddV = dvddV;
    }

    public PictureRenderData getDvddV() {
        return this.dvddV;
    }

    public void setDvddP(PictureRenderData dvddP) {
        this.dvddP = dvddP;
    }

    public PictureRenderData getDvddP() {
        return this.dvddP;
    }

    public void setZdhwV(PictureRenderData zdhwV) {
        this.zdhwV = zdhwV;
    }

    public PictureRenderData getZdhwV() {
        return this.zdhwV;
    }

    public void setZdhwP(PictureRenderData zdhwP) {
        this.zdhwP = zdhwP;
    }

    public PictureRenderData getZdhwP() {
        return this.zdhwP;
    }

    public void setDcyxV(PictureRenderData dcyxV) {
        this.dcyxV = dcyxV;
    }

    public PictureRenderData getDcyxV() {
        return this.dcyxV;
    }

    public void setDcyxP(PictureRenderData dcyxP) {
        this.dcyxP = dcyxP;
    }

    public PictureRenderData getDcyxP() {
        return this.dcyxP;
    }

    public void setYjqdV(PictureRenderData yjqdV) {
        this.yjqdV = yjqdV;
    }

    public PictureRenderData getYjqdV() {
        return this.yjqdV;
    }

    public void setYjqdP(PictureRenderData yjqdP) {
        this.yjqdP = yjqdP;
    }

    public PictureRenderData getYjqdP() {
        return this.yjqdP;
    }

    public void setDdtjV(PictureRenderData ddtjV) {
        this.ddtjV = ddtjV;
    }

    public PictureRenderData getDdtjV() {
        return this.ddtjV;
    }

    public void setDdtjP(PictureRenderData ddtjP) {
        this.ddtjP = ddtjP;
    }

    public PictureRenderData getDdtjP() {
        return this.ddtjP;
    }

    public void setDsxhV(PictureRenderData dsxhV) {
        this.dsxhV = dsxhV;
    }

    public PictureRenderData getDsxhV() {
        return this.dsxhV;
    }

    public void setDsxhP(PictureRenderData dsxhP) {
        this.dsxhP = dsxhP;
    }

    public PictureRenderData getDsxhP() {
        return this.dsxhP;
    }

    public void setMPlate(String mPlate) {
        this.mPlate = mPlate;
    }

    public String getMPlate() {
        return this.mPlate;
    }

    public void setMEngin(String mEngin) {
        this.mEngin = mEngin;
    }

    public String getMEngin() {
        return this.mEngin;
    }

    public void setMFirstp(String mFirstp) {
        this.mFirstp = mFirstp;
    }

    public String getMFirstp() {
        return this.mFirstp;
    }

    public void setMInsure(String mInsure) {
        this.mInsure = mInsure;
    }

    public String getMInsure() {
        return this.mInsure;
    }

    public void setMAnnual(String mAnnual) {
        this.mAnnual = mAnnual;
    }

    public String getMAnnual() {
        return this.mAnnual;
    }

    public void setMMile(String mMile) {
        this.mMile = mMile;
    }

    public String getMMile() {
        return this.mMile;
    }

    public void setMKey(String mKey) {
        this.mKey = mKey;
    }

    public String getMKey() {
        return this.mKey;
    }

}
