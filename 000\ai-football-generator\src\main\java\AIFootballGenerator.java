import java.io.*;
import java.util.Properties;
import java.util.Scanner;

/**
 * AI足球报名表生成器主程序
 * 通过与OpenAI GPT-4o-mini对话来收集球员信息并生成报名表
 */
public class AIFootballGenerator {
    private OpenAIClient aiClient;
    private PlayerDataParser dataParser;
    private FootballReportGenerator reportGenerator;
    private Properties config;
    private Scanner scanner;
    
    public AIFootballGenerator() {
        this.scanner = new Scanner(System.in);
        this.dataParser = new PlayerDataParser();
    }
    
    /**
     * 主程序入口
     */
    public static void main(String[] args) {
        AIFootballGenerator generator = new AIFootballGenerator();
        generator.run();
    }
    
    /**
     * 运行主程序
     */
    public void run() {
        try {
            // 1. 初始化配置
            if (!initializeConfig()) {
                return;
            }
            
            // 2. 初始化AI客户端
            if (!initializeAIClient()) {
                return;
            }
            
            // 3. 初始化报名表生成器
            initializeReportGenerator();
            
            // 4. 显示欢迎信息
            showWelcomeMessage();
            
            // 5. 开始AI对话
            startAIConversation();
            
        } catch (Exception e) {
            System.err.println("❌ 程序运行出错：" + e.getMessage());
            e.printStackTrace();
        } finally {
            scanner.close();
        }
    }
    
    /**
     * 初始化配置
     */
    private boolean initializeConfig() {
        try {
            config = new Properties();
            
            // 尝试从文件加载配置
            File configFile = new File("config.properties");
            if (configFile.exists()) {
                try (FileInputStream fis = new FileInputStream(configFile)) {
                    config.load(fis);
                }
            } else {
                System.err.println("❌ 配置文件 config.properties 不存在！");
                return false;
            }
            
            // 检查必需的配置项
            String apiKey = config.getProperty("openai.api.key");
            if (apiKey == null || apiKey.trim().isEmpty()) {
                System.err.println("❌ 请在 config.properties 中配置 OpenAI API 密钥！");
                return false;
            }
            
            System.out.println("✅ 配置加载成功");
            return true;
            
        } catch (Exception e) {
            System.err.println("❌ 加载配置失败：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 初始化AI客户端
     */
    private boolean initializeAIClient() {
        try {
            System.out.println("🤖 正在连接 OpenAI API...");
            aiClient = new OpenAIClient(config);
            
            // 测试连接
            if (aiClient.testConnection()) {
                System.out.println("✅ AI 服务连接成功");
                return true;
            } else {
                System.err.println("❌ AI 服务连接失败，请检查网络和API密钥");
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("❌ 初始化AI客户端失败：" + e.getMessage());
            return false;
        }
    }
    
    /**
     * 初始化报名表生成器
     */
    private void initializeReportGenerator() {
        String templatePath = config.getProperty("template.path", "template.docx");
        String outputDirectory = config.getProperty("output.directory", "output/");
        String photosDirectory = config.getProperty("photos.directory", "photos/");
        
        reportGenerator = new FootballReportGenerator(templatePath, outputDirectory, photosDirectory);
        System.out.println("✅ 报名表生成器初始化完成");
    }
    
    /**
     * 显示欢迎信息
     */
    private void showWelcomeMessage() {
        String separator = "============================================================";
        System.out.println("\n" + separator);
        System.out.println("🏆 " + config.getProperty("app.name", "AI足球报名表生成器"));
        System.out.println("📋 版本：" + config.getProperty("app.version", "1.0.0"));
        System.out.println("🤖 AI模型：" + config.getProperty("openai.model", "gpt-4o-mini"));
        System.out.println(separator);
        System.out.println("\n🎯 我是您的AI助手，我将帮助您收集足球队信息并生成报名表。");
        System.out.println("💡 您可以用自然语言告诉我球员信息，我会智能解析并生成专业的报名表。");
        System.out.println("\n📝 需要收集的信息包括：");
        System.out.println("   • 队伍基本信息：比赛标题、单位名称、领队、教练、队医");
        System.out.println("   • 球员信息：最多10名球员的号码、姓名、照片");
        System.out.println("\n💬 请开始输入，输入 'quit' 或 'exit' 退出程序\n");
    }
    
    /**
     * 开始AI对话
     */
    private void startAIConversation() {
        // 发送初始问候
        String initialResponse = aiClient.sendMessage("你好，我需要生成一份足球报名表，请开始收集信息。");
        System.out.println("🤖 AI助手：" + initialResponse);
        
        while (true) {
            System.out.print("\n👤 您：");
            String userInput = scanner.nextLine().trim();
            
            // 检查退出命令
            if (userInput.equalsIgnoreCase("quit") || 
                userInput.equalsIgnoreCase("exit") || 
                userInput.equalsIgnoreCase("退出")) {
                System.out.println("👋 感谢使用AI足球报名表生成器！");
                break;
            }
            
            if (userInput.isEmpty()) {
                continue;
            }
            
            // 发送用户输入给AI
            System.out.println("🤖 AI助手正在思考...");
            String aiResponse = aiClient.sendMessage(userInput);
            System.out.println("🤖 AI助手：" + aiResponse);
            
            // 检查是否包含完整数据
            if (dataParser.isCompleteData(aiResponse)) {
                System.out.println("\n🎉 信息收集完成！正在解析数据...");
                
                // 解析数据
                FootballTeamData teamData = dataParser.parseFromAIResponse(aiResponse);
                
                if (teamData != null && dataParser.validateTeamData(teamData)) {
                    // 显示解析结果
                    displayParsedData(teamData);
                    
                    // 询问是否生成报名表
                    if (confirmGeneration()) {
                        generateReport(teamData);
                    }
                    
                    // 询问是否继续
                    if (!askToContinue()) {
                        break;
                    }
                } else {
                    System.out.println("⚠️ 数据解析失败或不完整，请继续补充信息。");
                }
            }
        }
    }
    
    /**
     * 显示解析后的数据
     */
    private void displayParsedData(FootballTeamData teamData) {
        System.out.println("\n📋 解析到的队伍信息：");
        TeamInfo teamInfo = teamData.getTeamInfo();
        if (teamInfo != null) {
            System.out.println("   🏆 比赛标题：" + teamInfo.getTitle());
            System.out.println("   🏢 单位名称：" + teamInfo.getOrganizationName());
            System.out.println("   👨‍💼 领队：" + teamInfo.getTeamLeader());
            System.out.println("   👨‍🏫 教练：" + teamInfo.getCoach());
            System.out.println("   👨‍⚕️ 队医：" + teamInfo.getTeamDoctor());
        }
        
        System.out.println("\n👥 球员信息：");
        PlayerData[] players = teamData.getPlayers();
        int count = 0;
        for (int i = 0; i < players.length; i++) {
            if (players[i] != null) {
                count++;
                System.out.println("   " + count + ". " + players[i].getNumber() + "号 " + 
                                 players[i].getName() + " (照片: " + players[i].getPhotoPath() + ")");
            }
        }
        System.out.println("   📊 总计：" + count + " 名球员");
    }
    
    /**
     * 确认是否生成报名表
     */
    private boolean confirmGeneration() {
        System.out.print("\n❓ 是否生成报名表？(y/n): ");
        String response = scanner.nextLine().trim().toLowerCase();
        return response.equals("y") || response.equals("yes") || response.equals("是");
    }
    
    /**
     * 生成报名表
     */
    private void generateReport(FootballTeamData teamData) {
        System.out.println("\n🚀 正在生成报名表...");
        String outputPath = reportGenerator.generateReport(teamData);
        
        if (outputPath != null) {
            System.out.println("🎉 报名表生成成功！");
            System.out.println("📄 文件位置：" + outputPath);
        } else {
            System.out.println("❌ 报名表生成失败，请检查模板文件和照片。");
        }
    }
    
    /**
     * 询问是否继续
     */
    private boolean askToContinue() {
        System.out.print("\n❓ 是否继续生成其他报名表？(y/n): ");
        String response = scanner.nextLine().trim().toLowerCase();
        
        if (response.equals("y") || response.equals("yes") || response.equals("是")) {
            // 重置AI对话
            aiClient.resetConversation();
            System.out.println("\n🔄 已重置对话，请开始新的报名表生成...");
            return true;
        }
        
        return false;
    }
}
