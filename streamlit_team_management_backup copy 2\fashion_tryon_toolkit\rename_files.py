#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件重命名工具 - 将中文文件名改为简单英文名
File Rename Tool - Convert Chinese filenames to simple English names
"""

import os
import shutil
from pathlib import Path

def rename_batch_results():
    """重命名batch_results目录中的文件"""
    
    # 定义重命名映射
    rename_mapping = {
        "微信图片_2025-08-17_134343_347_final_white_background.png": "player01_final.png",
        "微信图片_2025-08-17_134416_824_final_white_background.png": "player02_final.png", 
        "微信图片_2025-08-17_134425_503_final_white_background.png": "player03_final.png",
        "微信图片_2025-08-17_134431_508_final_white_background.png": "player04_final.png",
        "微信图片_2025-08-17_134448_769_final_white_background.png": "player05_final.png",
        "微信图片_2025-08-18_030336_421_final_white_background.png": "player06_final.png",
        "微信图片_2025-08-19_095017_555_final_white_background.png": "player07_final.png",
        "微信图片_2025-08-19_095034_724_final_white_background.png": "player08_final.png",
        "微信图片_2025-08-19_095044_853_final_white_background.png": "player09_final.png",
        "微信图片_2025-08-19_095051_534_final_white_background.png": "player10_final.png",
        "微信图片_2025-08-19_095058_867_final_white_background.png": "player11_final.png",
        "微信图片_2025-08-19_095124_909_final_white_background.png": "player12_final.png",
        "微信图片_2025-08-19_095132_429_final_white_background.png": "player13_final.png",
        "微信图片_20250816210622_26_328.jpg": "clothing_template.jpg"
    }
    
    batch_results_dir = Path("batch_results")
    
    if not batch_results_dir.exists():
        print("❌ batch_results 目录不存在")
        return
    
    print("🔄 开始重命名文件...")
    print("=" * 60)
    
    success_count = 0
    total_count = len(rename_mapping)
    
    for old_name, new_name in rename_mapping.items():
        old_path = batch_results_dir / old_name
        new_path = batch_results_dir / new_name
        
        if old_path.exists():
            try:
                # 如果新文件名已存在，先删除
                if new_path.exists():
                    print(f"⚠️  目标文件已存在，将覆盖: {new_name}")
                    new_path.unlink()
                
                # 重命名文件
                old_path.rename(new_path)
                print(f"✅ {old_name}")
                print(f"   → {new_name}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ 重命名失败: {old_name}")
                print(f"   错误: {str(e)}")
        else:
            print(f"⚠️  文件不存在，跳过: {old_name}")
    
    print("=" * 60)
    print(f"📊 重命名完成: {success_count}/{total_count} 个文件")
    
    if success_count > 0:
        print(f"✅ 成功重命名 {success_count} 个文件")
        print("📁 新文件名格式:")
        print("   - player01_final.png ~ player13_final.png (球员照片)")
        print("   - clothing_template.jpg (服装模板)")
    
    return success_count

def list_current_files():
    """列出当前batch_results目录中的文件"""
    batch_results_dir = Path("batch_results")
    
    if not batch_results_dir.exists():
        print("❌ batch_results 目录不存在")
        return
    
    files = list(batch_results_dir.glob("*"))
    
    print("📁 当前 batch_results 目录中的文件:")
    print("=" * 60)
    
    if not files:
        print("   (目录为空)")
    else:
        for i, file_path in enumerate(sorted(files), 1):
            file_size = file_path.stat().st_size / 1024  # KB
            print(f"   {i:2d}. {file_path.name} ({file_size:.1f} KB)")
    
    print("=" * 60)
    print(f"总计: {len(files)} 个文件")

def main():
    """主函数"""
    print("🏷️  文件重命名工具")
    print("=" * 60)
    
    # 显示当前文件
    print("📋 重命名前:")
    list_current_files()
    
    print("\n🤔 确认要重命名这些文件吗？")
    print("   - 中文文件名将改为 player01_final.png ~ player13_final.png")
    print("   - 服装模板将改为 clothing_template.jpg")
    
    confirm = input("\n确认重命名？(y/n): ").strip().lower()
    
    if confirm != 'y':
        print("❌ 用户取消重命名")
        return
    
    # 执行重命名
    print("\n🔄 执行重命名...")
    success_count = rename_batch_results()
    
    # 显示重命名后的文件
    print("\n📋 重命名后:")
    list_current_files()
    
    if success_count > 0:
        print(f"\n🎉 重命名完成！成功处理 {success_count} 个文件")
        print("💡 现在可以更方便地使用这些文件了")
    else:
        print("\n😞 没有文件被重命名")

if __name__ == "__main__":
    main()
