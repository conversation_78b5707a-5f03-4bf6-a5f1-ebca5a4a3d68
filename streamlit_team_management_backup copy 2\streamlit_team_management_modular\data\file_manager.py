#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理器
File Manager

负责文件的上传、存储和管理
"""

import os
import shutil
from typing import Optional, List
import streamlit as st

from config.settings import app_settings
from utils.helpers import get_safe_team_name, ensure_directory_exists
from utils.image_utils import ImageProcessor


class FileManager:
    """文件管理器"""

    def __init__(self, user_id: str = None):
        if user_id:
            # 用户特定的文件夹
            self.upload_folder = os.path.join(app_settings.paths.UPLOAD_FOLDER, user_id)
            self.processed_folder = os.path.join(app_settings.paths.PROCESSED_PHOTOS_FOLDER, user_id)
            self.ai_export_folder = os.path.join(app_settings.paths.AI_EXPORT_FOLDER, user_id)
        else:
            # 全局文件夹（兼容旧版本）
            self.upload_folder = app_settings.paths.UPLOAD_FOLDER
            self.processed_folder = app_settings.paths.PROCESSED_PHOTOS_FOLDER
            self.ai_export_folder = app_settings.paths.AI_EXPORT_FOLDER

        # 确保目录存在
        for folder in [self.upload_folder, self.processed_folder, self.ai_export_folder]:
            ensure_directory_exists(folder)
    
    def get_team_upload_folder(self, team_name: str) -> str:
        """
        获取球队专属的上传文件夹
        
        Args:
            team_name: 球队名称
            
        Returns:
            str: 球队上传文件夹路径
        """
        safe_name = get_safe_team_name(team_name)
        team_folder = os.path.join(self.upload_folder, safe_name)
        ensure_directory_exists(team_folder)
        return team_folder
    
    def save_uploaded_photo(self, team_name: str, photo_file, filename: str) -> Optional[str]:
        """
        保存上传的照片
        
        Args:
            team_name: 球队名称
            photo_file: 上传的照片文件
            filename: 文件名
            
        Returns:
            Optional[str]: 保存的文件路径，如果失败返回None
        """
        try:
            team_folder = self.get_team_upload_folder(team_name)
            file_path = os.path.join(team_folder, filename)
            
            # 处理并保存图片
            if ImageProcessor.process_uploaded_image(
                photo_file, file_path, app_settings.MAX_IMAGE_SIZE
            ):
                return file_path
            else:
                return None
        except Exception as e:
            st.error(f"保存照片失败: {e}")
            return None
    
    def delete_photo(self, team_name: str, filename: str) -> bool:
        """
        删除照片文件
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            bool: 是否删除成功
        """
        try:
            team_folder = self.get_team_upload_folder(team_name)
            file_path = os.path.join(team_folder, filename)
            
            if os.path.exists(file_path):
                os.remove(file_path)
            return True
        except Exception as e:
            st.error(f"删除照片失败: {e}")
            return False
    
    def get_photo_path(self, team_name: str, filename: str) -> Optional[str]:
        """
        获取照片完整路径
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            Optional[str]: 照片路径，如果不存在返回None
        """
        if not filename:
            return None
        
        team_folder = self.get_team_upload_folder(team_name)
        file_path = os.path.join(team_folder, filename)
        
        return file_path if os.path.exists(file_path) else None
    
    def photo_exists(self, team_name: str, filename: str) -> bool:
        """
        检查照片是否存在
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            bool: 照片是否存在
        """
        return self.get_photo_path(team_name, filename) is not None
    
    def list_team_photos(self, team_name: str) -> List[str]:
        """
        列出球队所有照片
        
        Args:
            team_name: 球队名称
            
        Returns:
            List[str]: 照片文件名列表
        """
        team_folder = self.get_team_upload_folder(team_name)
        
        if not os.path.exists(team_folder):
            return []
        
        photos = []
        for filename in os.listdir(team_folder):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                photos.append(filename)
        
        return photos
    
    def save_template_image(self, template_file, filename: str) -> Optional[str]:
        """
        保存模板图片
        
        Args:
            template_file: 模板文件
            filename: 文件名
            
        Returns:
            Optional[str]: 保存的文件路径，如果失败返回None
        """
        try:
            templates_folder = os.path.join(self.processed_folder, "templates")
            ensure_directory_exists(templates_folder)
            
            file_path = os.path.join(templates_folder, filename)
            
            # 保存文件
            with open(file_path, "wb") as f:
                f.write(template_file.getvalue())
            
            return file_path
        except Exception as e:
            st.error(f"保存模板图片失败: {e}")
            return None
    
    def cleanup_team_files(self, team_name: str) -> bool:
        """
        清理球队相关文件
        
        Args:
            team_name: 球队名称
            
        Returns:
            bool: 是否清理成功
        """
        try:
            team_folder = self.get_team_upload_folder(team_name)
            
            if os.path.exists(team_folder):
                shutil.rmtree(team_folder)
            
            return True
        except Exception as e:
            st.error(f"清理球队文件失败: {e}")
            return False
    
    def get_file_size(self, team_name: str, filename: str) -> Optional[int]:
        """
        获取文件大小
        
        Args:
            team_name: 球队名称
            filename: 文件名
            
        Returns:
            Optional[int]: 文件大小（字节），如果文件不存在返回None
        """
        file_path = self.get_photo_path(team_name, filename)
        
        if file_path and os.path.exists(file_path):
            return os.path.getsize(file_path)
        
        return None
